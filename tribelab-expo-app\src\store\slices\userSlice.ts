import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { userAPI } from '../../services/api';

export interface UserProfile {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  bio?: string;
  location?: string;
  website?: string;
  socialLinks?: {
    twitter?: string;
    linkedin?: string;
    github?: string;
  };
  preferences: {
    notifications: {
      email: boolean;
      push: boolean;
      inApp: boolean;
    };
    privacy: {
      profileVisibility: 'public' | 'private' | 'friends';
      showEmail: boolean;
      showLocation: boolean;
    };
  };
  stats: {
    communitiesJoined: number;
    coursesCompleted: number;
    postsCreated: number;
    totalPoints: number;
  };
  subscription?: {
    plan: string;
    status: 'active' | 'inactive' | 'trial';
    expiresAt?: string;
  };
}

interface UserState {
  profile: UserProfile | null;
  isLoading: boolean;
  error: string | null;
  isUpdating: boolean;
}

const initialState: UserState = {
  profile: null,
  isLoading: false,
  error: null,
  isUpdating: false,
};

// Async thunks
export const fetchUserProfile = createAsyncThunk(
  'user/fetchProfile',
  async (_, { rejectWithValue }) => {
    try {
      const response = await userAPI.getProfile();
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch profile');
    }
  }
);

export const updateUserProfile = createAsyncThunk(
  'user/updateProfile',
  async (profileData: Partial<UserProfile>, { rejectWithValue }) => {
    try {
      const response = await userAPI.updateProfile(profileData);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update profile');
    }
  }
);

export const uploadProfileImage = createAsyncThunk(
  'user/uploadProfileImage',
  async (imageUri: string, { rejectWithValue }) => {
    try {
      const formData = new FormData();
      formData.append('image', {
        uri: imageUri,
        type: 'image/jpeg',
        name: 'profile.jpg',
      } as any);

      const response = await userAPI.uploadProfileImage(formData);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to upload image');
    }
  }
);

export const updateNotificationPreferences = createAsyncThunk(
  'user/updateNotificationPreferences',
  async (preferences: UserProfile['preferences']['notifications'], { rejectWithValue }) => {
    try {
      const response = await userAPI.updateProfile({ 
        preferences: { notifications: preferences } 
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update preferences');
    }
  }
);

export const updatePrivacySettings = createAsyncThunk(
  'user/updatePrivacySettings',
  async (privacy: UserProfile['preferences']['privacy'], { rejectWithValue }) => {
    try {
      const response = await userAPI.updateProfile({
        preferences: { privacy }
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update privacy settings');
    }
  }
);

export const searchUsers = createAsyncThunk(
  'user/searchUsers',
  async (query: string, { rejectWithValue }) => {
    try {
      const response = await fetch('/api/users');
      if (!response.ok) {
        throw new Error('Failed to fetch users');
      }
      const users = await response.json();

      // Filter users based on query if provided
      const filteredUsers = query.trim()
        ? users.filter((user: any) =>
            user.username?.toLowerCase().includes(query.toLowerCase()) ||
            user.name?.toLowerCase().includes(query.toLowerCase())
          )
        : users;

      return filteredUsers.map((user: any) => ({
        id: user._id,
        name: user.username,
        username: user.username,
        avatar: user.profileImage,
        isOnline: false, // This would need real-time data
      }));
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to search users');
    }
  }
);

// User slice
const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updateProfileLocally: (state, action: PayloadAction<Partial<UserProfile>>) => {
      if (state.profile) {
        state.profile = { ...state.profile, ...action.payload };
      }
    },
    incrementStat: (state, action: PayloadAction<keyof UserProfile['stats']>) => {
      if (state.profile) {
        state.profile.stats[action.payload]++;
      }
    },
    updateSubscription: (state, action: PayloadAction<UserProfile['subscription']>) => {
      if (state.profile) {
        state.profile.subscription = action.payload;
      }
    },
  },
  extraReducers: (builder) => {
    // Fetch profile
    builder
      .addCase(fetchUserProfile.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchUserProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        state.profile = action.payload;
      })
      .addCase(fetchUserProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Update profile
      .addCase(updateUserProfile.pending, (state) => {
        state.isUpdating = true;
        state.error = null;
      })
      .addCase(updateUserProfile.fulfilled, (state, action) => {
        state.isUpdating = false;
        state.profile = action.payload;
      })
      .addCase(updateUserProfile.rejected, (state, action) => {
        state.isUpdating = false;
        state.error = action.payload as string;
      })
      
      // Upload profile image
      .addCase(uploadProfileImage.pending, (state) => {
        state.isUpdating = true;
        state.error = null;
      })
      .addCase(uploadProfileImage.fulfilled, (state, action) => {
        state.isUpdating = false;
        if (state.profile) {
          state.profile.avatar = action.payload.imageUrl;
        }
      })
      .addCase(uploadProfileImage.rejected, (state, action) => {
        state.isUpdating = false;
        state.error = action.payload as string;
      })
      
      // Update notification preferences
      .addCase(updateNotificationPreferences.fulfilled, (state, action) => {
        if (state.profile) {
          state.profile.preferences.notifications = action.payload.preferences.notifications;
        }
      })
      
      // Update privacy settings
      .addCase(updatePrivacySettings.fulfilled, (state, action) => {
        if (state.profile) {
          state.profile.preferences.privacy = action.payload.preferences.privacy;
        }
      });
  },
});

export const { 
  clearError, 
  updateProfileLocally, 
  incrementStat, 
  updateSubscription 
} = userSlice.actions;
export default userSlice.reducer;
