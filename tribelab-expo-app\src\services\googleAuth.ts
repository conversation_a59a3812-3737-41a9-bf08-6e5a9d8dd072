import * as AuthSession from 'expo-auth-session';
import * as Web<PERSON>rowser from 'expo-web-browser';
import { Platform } from 'react-native';

// Complete the auth session for web
WebBrowser.maybeCompleteAuthSession();

// Google OAuth configuration
const GOOGLE_CLIENT_ID = {
  // These should be configured in your app.json or environment
  ios: process.env.EXPO_PUBLIC_GOOGLE_IOS_CLIENT_ID || '',
  android: process.env.EXPO_PUBLIC_GOOGLE_ANDROID_CLIENT_ID || '',
  web: process.env.EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID || '',
};

const redirectUri = AuthSession.makeRedirectUri({
  scheme: 'tribelab-expo-app',
});

export interface GoogleUser {
  id: string;
  email: string;
  name: string;
  picture?: string;
  given_name?: string;
  family_name?: string;
}

export class GoogleAuthService {
  private static instance: GoogleAuthService;
  private request: AuthSession.AuthRequest | null = null;
  private discovery: AuthSession.DiscoveryDocument | null = null;

  private constructor() {
    // Don't initialize automatically to avoid startup errors
    // Initialize only when Google sign-in is actually requested
  }

  public static getInstance(): GoogleAuthService {
    if (!GoogleAuthService.instance) {
      GoogleAuthService.instance = new GoogleAuthService();
    }
    return GoogleAuthService.instance;
  }

  private async initializeAuth() {
    try {
      const clientId = this.getClientId();

      // Check if client ID is properly configured
      if (!clientId || clientId.includes('your-') || clientId === '') {
        console.warn('Google OAuth client ID not properly configured. Google sign-in will be disabled.');
        return;
      }

      // Google's OAuth 2.0 discovery document with timeout and error handling
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      try {
        this.discovery = await AuthSession.fetchDiscoveryAsync(
          'https://accounts.google.com/.well-known/openid_configuration'
        );
        clearTimeout(timeoutId);
      } catch (fetchError) {
        clearTimeout(timeoutId);
        console.warn('Failed to fetch Google discovery document, using fallback configuration');

        // Fallback discovery configuration
        this.discovery = {
          authorizationEndpoint: 'https://accounts.google.com/o/oauth2/v2/auth',
          tokenEndpoint: 'https://oauth2.googleapis.com/token',
          userInfoEndpoint: 'https://www.googleapis.com/oauth2/v2/userinfo',
          revocationEndpoint: 'https://oauth2.googleapis.com/revoke',
        };
      }

      // Create the auth request
      this.request = new AuthSession.AuthRequest({
        clientId,
        scopes: ['openid', 'profile', 'email'],
        redirectUri,
        responseType: AuthSession.ResponseType.Code,
        extraParams: {
          access_type: 'offline',
        },
      });
    } catch (error) {
      console.error('Failed to initialize Google Auth:', error);
    }
  }

  private getClientId(): string {
    if (Platform.OS === 'ios') {
      return GOOGLE_CLIENT_ID.ios;
    } else if (Platform.OS === 'android') {
      return GOOGLE_CLIENT_ID.android;
    } else {
      return GOOGLE_CLIENT_ID.web;
    }
  }

  public async signIn(): Promise<GoogleUser | null> {
    try {
      const clientId = this.getClientId();

      // Check if Google Auth is properly configured
      if (!clientId || clientId.includes('your-') || clientId === '') {
        throw new Error('Google Sign-In is currently unavailable. Please use email and password to login, or contact support to enable Google authentication.');
      }

      if (!this.request || !this.discovery) {
        await this.initializeAuth();
      }

      if (!this.request || !this.discovery) {
        throw new Error('Failed to initialize Google Auth. Please check your network connection and try again.');
      }

      // Prompt the user to authenticate
      const result = await this.request.promptAsync(this.discovery);

      if (result.type === 'success') {
        // Exchange the authorization code for an access token
        const tokenResult = await AuthSession.exchangeCodeAsync(
          {
            clientId: this.getClientId(),
            code: result.params.code,
            extraParams: {
              code_verifier: this.request.codeVerifier || '',
            },
            redirectUri,
          },
          this.discovery
        );

        // Get user info from Google
        const userInfo = await this.getUserInfo(tokenResult.accessToken);
        return userInfo;
      } else if (result.type === 'cancel') {
        console.log('User cancelled Google sign-in');
        return null;
      } else {
        throw new Error('Google sign-in failed');
      }
    } catch (error) {
      console.error('Google sign-in error:', error);
      throw error;
    }
  }

  private async getUserInfo(accessToken: string): Promise<GoogleUser> {
    try {
      const response = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch user info from Google');
      }

      const userInfo = await response.json();
      return {
        id: userInfo.id,
        email: userInfo.email,
        name: userInfo.name,
        picture: userInfo.picture,
        given_name: userInfo.given_name,
        family_name: userInfo.family_name,
      };
    } catch (error) {
      console.error('Failed to get user info:', error);
      throw error;
    }
  }

  public async signOut(): Promise<void> {
    try {
      // For Google OAuth, we mainly need to clear local tokens
      // The actual sign-out from Google would require additional setup
      console.log('Google sign-out completed');
    } catch (error) {
      console.error('Google sign-out error:', error);
      throw error;
    }
  }
}

export const googleAuthService = GoogleAuthService.getInstance();
