import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "@/lib/auth-helpers";
import { dbconnect } from "@/lib/db";
import { User } from "@/models/User";
import { Payment } from "@/models/Payment";

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    await dbconnect();

    // Check if user is admin
    const user = await User.findById(session.user.id);
    if (!user || !user.isAdmin) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const period = searchParams.get("period") || "30d";

    // Calculate date range and intervals
    const now = new Date();
    let startDate: Date;
    let intervalDays: number;
    
    switch (period) {
      case "7d":
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        intervalDays = 1; // Daily intervals
        break;
      case "30d":
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        intervalDays = 1; // Daily intervals
        break;
      case "90d":
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        intervalDays = 7; // Weekly intervals
        break;
      case "1y":
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        intervalDays = 30; // Monthly intervals
        break;
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        intervalDays = 1;
    }

    // Generate date intervals
    const intervals = [];
    const currentDate = new Date(startDate);
    
    while (currentDate <= now) {
      intervals.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + intervalDays);
    }

    // Get revenue data for each interval
    const revenueData = await Promise.all(
      intervals.map(async (intervalStart, index) => {
        const intervalEnd = index < intervals.length - 1 
          ? intervals[index + 1] 
          : now;

        const revenue = await Payment.aggregate([
          {
            $match: {
              status: "completed",
              createdAt: {
                $gte: intervalStart,
                $lt: intervalEnd
              }
            }
          },
          {
            $group: {
              _id: null,
              total: { $sum: "$amount" },
              count: { $sum: 1 }
            }
          }
        ]);

        return {
          date: intervalStart.toISOString().split('T')[0],
          revenue: revenue[0]?.total || 0,
          transactions: revenue[0]?.count || 0
        };
      })
    );

    // Calculate totals and averages
    const totalRevenue = revenueData.reduce((sum, item) => sum + item.revenue, 0);
    const totalTransactions = revenueData.reduce((sum, item) => sum + item.transactions, 0);
    const averageRevenue = revenueData.length > 0 ? totalRevenue / revenueData.length : 0;

    const chartData = {
      data: revenueData,
      summary: {
        totalRevenue,
        totalTransactions,
        averageRevenue: Math.round(averageRevenue * 100) / 100,
        period
      }
    };

    return NextResponse.json(chartData);

  } catch (error) {
    console.error("Error fetching revenue chart data:", error);
    return NextResponse.json(
      { error: "Failed to fetch revenue chart data" },
      { status: 500 }
    );
  }
}
