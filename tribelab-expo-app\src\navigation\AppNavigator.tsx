import React, { useEffect } from "react";
import { NavigationContainer } from "@react-navigation/native";
import { ActivityIndicator, View, StyleSheet } from "react-native";
import { useAppDispatch, useAppSelector } from "../hooks/redux";
import { checkAuthStatus } from "../store/slices/authSlice";
import { useSessionManager } from "../hooks/useSessionManager";

import AuthNavigator from "./AuthNavigator";
import MainNavigator from "./MainNavigator";

// Component that uses session manager inside NavigationContainer
const NavigatorContent = () => {
  const { isAuthenticated } = useAppSelector((state) => state.auth);

  // Initialize session management - now inside NavigationContainer
  useSessionManager();

  return isAuthenticated ? <MainNavigator /> : <AuthNavigator />;
};

const AppNavigator = () => {
  const dispatch = useAppDispatch();
  const { isLoading } = useAppSelector((state) => state.auth);

  useEffect(() => {
    // Check authentication status on app start
    dispatch(checkAuthStatus());
  }, [dispatch]);

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
      </View>
    );
  }

  return (
    <NavigationContainer>
      <NavigatorContent />
    </NavigationContainer>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#fff",
  },
});

export default AppNavigator;
