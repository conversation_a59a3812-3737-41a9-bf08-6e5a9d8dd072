import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "@/lib/auth-helpers";
import { dbconnect } from "@/lib/db";
import { User } from "@/models/User";
import { Community } from "@/models/Community";

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    await dbconnect();

    // Check if user is admin
    const user = await User.findById(session.user.id);
    if (!user || !user.isAdmin) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const period = searchParams.get("period") || "30d";

    // Calculate date range and intervals
    const now = new Date();
    let startDate: Date;
    let intervalDays: number;
    
    switch (period) {
      case "7d":
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        intervalDays = 1; // Daily intervals
        break;
      case "30d":
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        intervalDays = 1; // Daily intervals
        break;
      case "90d":
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        intervalDays = 7; // Weekly intervals
        break;
      case "1y":
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        intervalDays = 30; // Monthly intervals
        break;
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        intervalDays = 1;
    }

    // Generate date intervals
    const intervals = [];
    const currentDate = new Date(startDate);
    
    while (currentDate <= now) {
      intervals.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + intervalDays);
    }

    // Get user and community growth data for each interval
    const growthData = await Promise.all(
      intervals.map(async (intervalStart, index) => {
        const intervalEnd = index < intervals.length - 1 
          ? intervals[index + 1] 
          : now;

        const newUsers = await User.countDocuments({
          createdAt: {
            $gte: intervalStart,
            $lt: intervalEnd
          }
        });

        const newCommunities = await Community.countDocuments({
          createdAt: {
            $gte: intervalStart,
            $lt: intervalEnd
          }
        });

        // Get cumulative totals up to this point
        const totalUsers = await User.countDocuments({
          createdAt: { $lt: intervalEnd }
        });

        const totalCommunities = await Community.countDocuments({
          createdAt: { $lt: intervalEnd }
        });

        return {
          date: intervalStart.toISOString().split('T')[0],
          newUsers,
          newCommunities,
          totalUsers,
          totalCommunities
        };
      })
    );

    // Calculate growth rates
    const dataWithGrowth = growthData.map((item, index) => {
      if (index === 0) {
        return {
          ...item,
          userGrowthRate: 0,
          communityGrowthRate: 0
        };
      }

      const previousItem = growthData[index - 1];
      const userGrowthRate = previousItem.totalUsers > 0 
        ? ((item.totalUsers - previousItem.totalUsers) / previousItem.totalUsers * 100)
        : 0;
      const communityGrowthRate = previousItem.totalCommunities > 0 
        ? ((item.totalCommunities - previousItem.totalCommunities) / previousItem.totalCommunities * 100)
        : 0;

      return {
        ...item,
        userGrowthRate: Math.round(userGrowthRate * 100) / 100,
        communityGrowthRate: Math.round(communityGrowthRate * 100) / 100
      };
    });

    // Calculate summary statistics
    const totalNewUsers = growthData.reduce((sum, item) => sum + item.newUsers, 0);
    const totalNewCommunities = growthData.reduce((sum, item) => sum + item.newCommunities, 0);
    const averageUserGrowth = dataWithGrowth.length > 1 
      ? dataWithGrowth.slice(1).reduce((sum, item) => sum + item.userGrowthRate, 0) / (dataWithGrowth.length - 1)
      : 0;

    const chartData = {
      data: dataWithGrowth,
      summary: {
        totalNewUsers,
        totalNewCommunities,
        averageUserGrowth: Math.round(averageUserGrowth * 100) / 100,
        period
      }
    };

    return NextResponse.json(chartData);

  } catch (error) {
    console.error("Error fetching user growth data:", error);
    return NextResponse.json(
      { error: "Failed to fetch user growth data" },
      { status: 500 }
    );
  }
}
