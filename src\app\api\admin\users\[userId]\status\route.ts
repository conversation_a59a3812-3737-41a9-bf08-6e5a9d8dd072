import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "@/lib/auth-helpers";
import { dbconnect } from "@/lib/db";
import { User } from "@/models/User";

export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ userId: string }> }
) {
  try {
    const session = await getServerSession();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    await dbconnect();

    // Check if user is admin
    const adminUser = await User.findById(session.user.id);
    if (!adminUser || !adminUser.isAdmin) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }

    const params = await context.params;
    const { userId } = params;
    const { status, reason } = await request.json();

    // Validate input
    if (!status || !reason) {
      return NextResponse.json(
        { error: "Status and reason are required" },
        { status: 400 }
      );
    }

    const validStatuses = ["active", "suspended", "banned"];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: "Invalid status. Must be one of: active, suspended, banned" },
        { status: 400 }
      );
    }

    // Find the user to update
    const targetUser = await User.findById(userId);
    if (!targetUser) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Prevent admin from changing their own status
    if (targetUser._id.toString() === session.user.id) {
      return NextResponse.json(
        { error: "Cannot change your own status" },
        { status: 400 }
      );
    }

    // Update user status
    targetUser.status = status;
    
    // Add to admin actions log (if you have an AdminAction model)
    // This would be useful for audit trails
    const adminAction = {
      adminId: session.user.id,
      targetUserId: userId,
      action: `status_change_${status}`,
      reason,
      timestamp: new Date(),
      previousStatus: targetUser.status
    };

    // Save the admin action to user's history or a separate collection
    if (!targetUser.adminActions) {
      targetUser.adminActions = [];
    }
    targetUser.adminActions.push(adminAction);

    await targetUser.save();

    // Log the action for audit purposes
    console.log(`Admin ${adminUser.username} changed user ${targetUser.username} status to ${status}. Reason: ${reason}`);

    return NextResponse.json({
      success: true,
      message: `User status updated to ${status}`,
      user: {
        id: targetUser._id,
        username: targetUser.username,
        email: targetUser.email,
        status: targetUser.status,
        updatedAt: new Date()
      },
      adminAction
    });

  } catch (error) {
    console.error("Error updating user status:", error);
    return NextResponse.json(
      { error: "Failed to update user status" },
      { status: 500 }
    );
  }
}
