import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "@/lib/auth-helpers";
import { dbconnect } from "@/lib/db";
import { User } from "@/models/User";
import { Community } from "@/models/Community";
import { Course } from "@/models/Course";
import { Payment } from "@/models/Payment";

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    await dbconnect();

    // Check if user is admin
    const user = await User.findById(session.user.id);
    if (!user || !user.isAdmin) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const period = searchParams.get("period") || "30d";

    // Calculate date range based on period
    const now = new Date();
    let startDate: Date;
    
    switch (period) {
      case "7d":
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case "30d":
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case "90d":
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      case "1y":
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }

    // Get total counts
    const totalUsers = await User.countDocuments();
    const totalCommunities = await Community.countDocuments();
    const totalCourses = await Course.countDocuments();

    // Get new counts for the period
    const newUsers = await User.countDocuments({
      createdAt: { $gte: startDate }
    });
    const newCommunities = await Community.countDocuments({
      createdAt: { $gte: startDate }
    });
    const newCourses = await Course.countDocuments({
      createdAt: { $gte: startDate }
    });

    // Get revenue data
    const totalRevenue = await Payment.aggregate([
      { $match: { status: "completed" } },
      { $group: { _id: null, total: { $sum: "$amount" } } }
    ]);

    const periodRevenue = await Payment.aggregate([
      { 
        $match: { 
          status: "completed",
          createdAt: { $gte: startDate }
        } 
      },
      { $group: { _id: null, total: { $sum: "$amount" } } }
    ]);

    // Calculate growth percentages
    const previousPeriodStart = new Date(startDate.getTime() - (now.getTime() - startDate.getTime()));
    
    const previousUsers = await User.countDocuments({
      createdAt: { $gte: previousPeriodStart, $lt: startDate }
    });
    const previousCommunities = await Community.countDocuments({
      createdAt: { $gte: previousPeriodStart, $lt: startDate }
    });
    const previousRevenue = await Payment.aggregate([
      { 
        $match: { 
          status: "completed",
          createdAt: { $gte: previousPeriodStart, $lt: startDate }
        } 
      },
      { $group: { _id: null, total: { $sum: "$amount" } } }
    ]);

    const userGrowth = previousUsers > 0 ? ((newUsers - previousUsers) / previousUsers * 100) : 0;
    const communityGrowth = previousCommunities > 0 ? ((newCommunities - previousCommunities) / previousCommunities * 100) : 0;
    const revenueGrowth = previousRevenue[0]?.total > 0 ? 
      ((periodRevenue[0]?.total || 0) - previousRevenue[0].total) / previousRevenue[0].total * 100 : 0;

    const stats = {
      totalUsers,
      totalCommunities,
      totalCourses,
      totalRevenue: totalRevenue[0]?.total || 0,
      newUsers,
      newCommunities,
      newCourses,
      periodRevenue: periodRevenue[0]?.total || 0,
      userGrowth: Math.round(userGrowth * 100) / 100,
      communityGrowth: Math.round(communityGrowth * 100) / 100,
      revenueGrowth: Math.round(revenueGrowth * 100) / 100,
      period
    };

    return NextResponse.json(stats);

  } catch (error) {
    console.error("Error fetching admin stats:", error);
    return NextResponse.json(
      { error: "Failed to fetch admin stats" },
      { status: 500 }
    );
  }
}
