import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Image,
  ActivityIndicator,
  Alert,
  RefreshControl,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import Icon from "react-native-vector-icons/Ionicons";
import { useNavigation } from "@react-navigation/native";
import { useAppDispatch, useAppSelector } from "../hooks/redux";
import { fetchCommunities } from "../store/slices/communitySlice";
import Toast from "react-native-toast-message";
import { handleApiError } from "../utils/errorHandler";
import LoadingState from "../components/LoadingState";
import { getSafeImageSource } from "../utils/imageUtils";

interface Community {
  _id: string;
  name: string;
  description?: string;
  iconImageUrl?: string;
  bannerImageurl?: string;
  category?: string;
  memberCount: number;
  isPrivate: boolean;
  isJoined: boolean;
  tags?: string[];
  featured?: boolean;
  createdAt: string;
  admin: {
    _id: string;
    username: string;
    name: string;
    profileImage?: string;
  };
  members: string[];
}

const CommunityDirectoryScreen = () => {
  const navigation = useNavigation();
  const dispatch = useAppDispatch();
  const { communities, isLoading } = useAppSelector((state) => state.community);

  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [sortBy, setSortBy] = useState<"popular" | "newest" | "activity">("popular");

  const categories = [
    "All",
    "Technology",
    "Business",
    "Education",
    "Health & Fitness",
    "Arts & Culture",
    "Sports",
    "Gaming",
    "Lifestyle",
  ];

  useEffect(() => {
    loadCommunities();
  }, []);

  const loadCommunities = async () => {
    try {
      await dispatch(fetchCommunities({ page: 1, limit: 20 })).unwrap();
    } catch (error) {
      handleApiError(error);
    }
  };

  // Filter communities based on search and category
  const filteredCommunities = React.useMemo(() => {
    let filtered = communities;

    // Filter by search query
    if (searchQuery.trim()) {
      filtered = filtered.filter(
        (community) =>
          community.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          community.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          community.tags?.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Filter by category
    if (selectedCategory !== "All") {
      filtered = filtered.filter(
        (community) => community.category === selectedCategory
      );
    }

    // Sort communities
    switch (sortBy) {
      case "newest":
        filtered = [...filtered].sort(
          (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        );
        break;
      case "activity":
        // Sort by member count as a proxy for activity
        filtered = [...filtered].sort((a, b) => b.memberCount - a.memberCount);
        break;
      case "popular":
      default:
        filtered = [...filtered].sort((a, b) => b.memberCount - a.memberCount);
        break;
    }

    return filtered;
  }, [communities, searchQuery, selectedCategory, sortBy]);

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadCommunities();
    setRefreshing(false);
  };

  const handleJoinCommunity = async (communityId: string) => {
    try {
      // TODO: Implement join community API call
      // For now, show a message that this feature needs backend integration
      Alert.alert(
        "Join Community",
        "This feature will be implemented with the backend integration.",
        [{ text: "OK" }]
      );
    } catch (error) {
      Toast.show({
        type: "error",
        text1: "Error",
        text2: "Failed to join community",
      });
    }
  };

  const handleCommunityPress = (community: Community) => {
    navigation.navigate("CommunityAbout" as never, { communityId: community._id } as never);
  };

  // Removed getActivityColor function since backend doesn't provide activity levels

  const formatMemberCount = (count: number) => {
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  const featuredCommunities = filteredCommunities.filter(c => c.featured);
  const regularCommunities = filteredCommunities.filter(c => !c.featured);

  if (isLoading && communities.length === 0) {
    return (
      <SafeAreaView style={styles.container}>
        <LoadingState loading={true} />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Icon name="arrow-back" size={24} color="#000" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Discover Communities</Text>
        <TouchableOpacity onPress={() => navigation.navigate("CommunityCreate" as never)}>
          <Icon name="add" size={24} color="#000" />
        </TouchableOpacity>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <Icon name="search" size={20} color="#666" />
          <TextInput
            style={styles.searchInput}
            placeholder="Search communities..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery("")}>
              <Icon name="close" size={20} color="#666" />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Filters */}
      <View style={styles.filtersContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={styles.categoryFilters}>
            {categories.map((category) => (
              <TouchableOpacity
                key={category}
                style={[
                  styles.categoryButton,
                  selectedCategory === category && styles.categoryButtonActive,
                ]}
                onPress={() => setSelectedCategory(category)}
              >
                <Text
                  style={[
                    styles.categoryButtonText,
                    selectedCategory === category && styles.categoryButtonTextActive,
                  ]}
                >
                  {category}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
        
        <View style={styles.sortContainer}>
          <TouchableOpacity
            style={styles.sortButton}
            onPress={() => {
              const options = ["popular", "newest", "activity"];
              const currentIndex = options.indexOf(sortBy);
              const nextIndex = (currentIndex + 1) % options.length;
              setSortBy(options[nextIndex] as any);
            }}
          >
            <Icon name="funnel" size={16} color="#666" />
            <Text style={styles.sortText}>
              {sortBy === "popular" ? "Popular" : sortBy === "newest" ? "Newest" : "Activity"}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView 
        style={styles.content} 
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        {filteredCommunities.length === 0 && !isLoading ? (
          <LoadingState
            showEmpty={true}
            emptyMessage={searchQuery ? "No communities found matching your search" : "No communities available"}
          />
        ) : (
          <>
            {/* Featured Communities */}
            {featuredCommunities.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Featured Communities</Text>
            {featuredCommunities.map((community) => (
              <TouchableOpacity
                key={community._id}
                style={styles.featuredCard}
                onPress={() => handleCommunityPress(community)}
              >
                <Image
                  source={getSafeImageSource(community.bannerImageurl, community.name, 'banner')}
                  style={styles.featuredBanner}
                />
                <View style={styles.featuredContent}>
                  <View style={styles.featuredHeader}>
                    <Image
                      source={{ uri: community.iconImageUrl || "https://via.placeholder.com/60" }}
                      style={styles.featuredAvatar}
                    />
                    <View style={styles.featuredInfo}>
                      <Text style={styles.featuredName}>{community.name}</Text>
                      <Text style={styles.featuredCategory}>{community.category || "General"}</Text>
                    </View>
                    <View style={styles.featuredBadge}>
                      <Icon name="star" size={12} color="#FFD700" />
                      <Text style={styles.featuredBadgeText}>Featured</Text>
                    </View>
                  </View>
                  <Text style={styles.featuredDescription}>{community.description || "No description available"}</Text>
                  <View style={styles.featuredFooter}>
                    <View style={styles.featuredStats}>
                      <Text style={styles.featuredMembers}>
                        {formatMemberCount(community.memberCount)} members
                      </Text>
                      <Text style={styles.featuredActivity}>Active community</Text>
                    </View>
                    <TouchableOpacity
                      style={[
                        styles.joinButton,
                        community.isJoined && styles.joinedButton,
                      ]}
                      onPress={() => handleJoinCommunity(community._id)}
                    >
                      <Text style={[
                        styles.joinButtonText,
                        community.isJoined && styles.joinedButtonText,
                      ]}>
                        {community.isJoined ? "Joined" : "Join"}
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        )}

        {/* All Communities */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            All Communities ({filteredCommunities.length})
          </Text>
          
          {regularCommunities.length === 0 ? (
            <View style={styles.emptyState}>
              <Icon name="search" size={40} color="#ccc" />
              <Text style={styles.emptyStateText}>No communities found</Text>
              <Text style={styles.emptyStateSubtext}>
                Try adjusting your search or filters
              </Text>
            </View>
          ) : (
            regularCommunities.map((community) => (
              <TouchableOpacity
                key={community._id}
                style={styles.communityCard}
                onPress={() => handleCommunityPress(community)}
              >
                <Image
                  source={{ uri: community.iconImageUrl || "https://via.placeholder.com/60" }}
                  style={styles.communityAvatar}
                />
                <View style={styles.communityInfo}>
                  <View style={styles.communityHeader}>
                    <Text style={styles.communityName}>{community.name}</Text>
                    <View style={styles.communityMeta}>
                      <Text style={styles.communityCategory}>{community.category || "General"}</Text>
                      {community.isPrivate && (
                        <Icon name="lock-closed" size={12} color="#666" />
                      )}
                    </View>
                  </View>
                  <Text style={styles.communityDescription} numberOfLines={2}>
                    {community.description || "No description available"}
                  </Text>
                  <View style={styles.communityFooter}>
                    <View style={styles.communityStats}>
                      <Text style={styles.communityMembers}>
                        {formatMemberCount(community.memberCount)} members
                      </Text>
                      <Text style={styles.communityActivity}>Active</Text>
                    </View>
                    <TouchableOpacity
                      style={[
                        styles.joinButton,
                        community.isJoined && styles.joinedButton,
                      ]}
                      onPress={() => handleJoinCommunity(community._id)}
                    >
                      <Text style={[
                        styles.joinButtonText,
                        community.isJoined && styles.joinedButtonText,
                      ]}>
                        {community.isJoined ? "Joined" : "Join"}
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </TouchableOpacity>
            ))
          )}
        </View>
            </>
          )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  searchBar: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f0f0f0",
    borderRadius: 25,
    paddingHorizontal: 15,
    paddingVertical: 10,
    gap: 10,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: "#333",
  },
  filtersContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  categoryFilters: {
    flexDirection: "row",
    gap: 10,
  },
  categoryButton: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: "#f0f0f0",
  },
  categoryButtonActive: {
    backgroundColor: "#000",
  },
  categoryButtonText: {
    fontSize: 14,
    color: "#666",
  },
  categoryButtonTextActive: {
    color: "#fff",
  },
  sortContainer: {
    marginLeft: 10,
  },
  sortButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 5,
    paddingHorizontal: 10,
    paddingVertical: 5,
  },
  sortText: {
    fontSize: 14,
    color: "#666",
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: "#666",
  },
  section: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "600",
    marginBottom: 15,
  },
  featuredCard: {
    backgroundColor: "#f9f9f9",
    borderRadius: 12,
    marginBottom: 15,
    overflow: "hidden",
  },
  featuredBanner: {
    width: "100%",
    height: 120,
    resizeMode: "cover",
  },
  featuredContent: {
    padding: 15,
  },
  featuredHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 10,
  },
  featuredAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  featuredInfo: {
    flex: 1,
  },
  featuredName: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 2,
  },
  featuredCategory: {
    fontSize: 12,
    color: "#666",
  },
  featuredBadge: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#fff3cd",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  featuredBadgeText: {
    fontSize: 10,
    color: "#856404",
    fontWeight: "600",
  },
  featuredDescription: {
    fontSize: 14,
    color: "#666",
    lineHeight: 20,
    marginBottom: 12,
  },
  featuredFooter: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  featuredStats: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  featuredMembers: {
    fontSize: 12,
    color: "#666",
  },
  featuredActivity: {
    fontSize: 12,
    color: "#666",
  },
  communityCard: {
    flexDirection: "row",
    backgroundColor: "#f9f9f9",
    borderRadius: 12,
    padding: 15,
    marginBottom: 10,
  },
  communityAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 15,
  },
  communityInfo: {
    flex: 1,
  },
  communityHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 5,
  },
  communityName: {
    fontSize: 16,
    fontWeight: "600",
    flex: 1,
  },
  communityMeta: {
    flexDirection: "row",
    alignItems: "center",
    gap: 5,
  },
  communityCategory: {
    fontSize: 12,
    color: "#666",
    backgroundColor: "#fff",
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
  },
  communityDescription: {
    fontSize: 14,
    color: "#666",
    lineHeight: 20,
    marginBottom: 10,
  },
  communityFooter: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  communityStats: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  communityMembers: {
    fontSize: 12,
    color: "#666",
  },
  communityActivity: {
    fontSize: 12,
    color: "#666",
  },
  activityDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
  },
  joinButton: {
    backgroundColor: "#000",
    paddingHorizontal: 15,
    paddingVertical: 6,
    borderRadius: 15,
  },
  joinedButton: {
    backgroundColor: "#f0f0f0",
    borderWidth: 1,
    borderColor: "#ddd",
  },
  joinButtonText: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "600",
  },
  joinedButtonText: {
    color: "#666",
  },
  emptyState: {
    alignItems: "center",
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 16,
    fontWeight: "500",
    color: "#666",
    marginTop: 10,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: "#999",
    marginTop: 5,
  },
});

export default CommunityDirectoryScreen;
