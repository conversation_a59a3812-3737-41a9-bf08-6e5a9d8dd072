import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "@/lib/auth-helpers";
import { dbconnect } from "@/lib/db";
import { User } from "@/models/User";
import { Community } from "@/models/Community";
import { Course } from "@/models/Course";
import { Post } from "@/models/Post";
import { Payment } from "@/models/Payment";

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    await dbconnect();

    // Check if user is admin
    const user = await User.findById(session.user.id);
    if (!user || !user.isAdmin) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const search = searchParams.get("search") || "";
    const role = searchParams.get("role") || "";
    const status = searchParams.get("status") || "";

    // Build query
    const query: any = {};
    
    if (search) {
      query.$or = [
        { username: { $regex: search, $options: "i" } },
        { email: { $regex: search, $options: "i" } },
        { name: { $regex: search, $options: "i" } }
      ];
    }

    if (role && role !== "all") {
      query.role = role;
    }

    if (status && status !== "all") {
      query.status = status;
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get users with pagination
    const users = await User.find(query)
      .select("-password -refreshToken")
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    // Get total count for pagination
    const totalUsers = await User.countDocuments(query);

    // Enhance users with additional stats
    const enhancedUsers = await Promise.all(
      users.map(async (user) => {
        // Get user stats
        const communitiesJoined = await Community.countDocuments({
          members: user._id
        });

        const coursesCompleted = await Course.countDocuments({
          enrolledUsers: user._id,
          "enrolledUsers.completedAt": { $exists: true }
        });

        let postsCreated = 0;
        try {
          postsCreated = await Post.countDocuments({
            authorId: user._id
          });
        } catch (error) {
          // Post model might not exist
        }

        const totalSpent = await Payment.aggregate([
          {
            $match: {
              userId: user._id,
              status: "completed"
            }
          },
          {
            $group: {
              _id: null,
              total: { $sum: "$amount" }
            }
          }
        ]);

        return {
          id: user._id,
          name: user.name || user.username,
          email: user.email,
          username: user.username,
          avatar: user.profileImage,
          role: user.role || "user",
          status: user.status || "active",
          isEmailVerified: user.isEmailVerified || false,
          lastLoginAt: user.lastLoginAt,
          createdAt: user.createdAt,
          stats: {
            communitiesJoined,
            coursesCompleted,
            postsCreated,
            totalSpent: totalSpent[0]?.total || 0
          }
        };
      })
    );

    const hasMore = skip + limit < totalUsers;

    return NextResponse.json({
      users: enhancedUsers,
      pagination: {
        page,
        limit,
        total: totalUsers,
        hasMore,
        totalPages: Math.ceil(totalUsers / limit)
      }
    });

  } catch (error) {
    console.error("Error fetching admin users:", error);
    return NextResponse.json(
      { error: "Failed to fetch users" },
      { status: 500 }
    );
  }
}
