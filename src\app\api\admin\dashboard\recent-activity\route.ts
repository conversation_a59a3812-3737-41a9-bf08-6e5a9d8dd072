import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "@/lib/auth-helpers";
import { dbconnect } from "@/lib/db";
import { User } from "@/models/User";
import { Community } from "@/models/Community";
import { Course } from "@/models/Course";
import { Payment } from "@/models/Payment";
import { Post } from "@/models/Post";

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    await dbconnect();

    // Check if user is admin
    const user = await User.findById(session.user.id);
    if (!user || !user.isAdmin) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "20");

    const activities = [];

    // Get recent user registrations
    const recentUsers = await User.find({})
      .sort({ createdAt: -1 })
      .limit(5)
      .select("username email createdAt");

    recentUsers.forEach(user => {
      activities.push({
        id: `user-${user._id}`,
        type: "user_registration",
        title: "New User Registration",
        description: `${user.username || user.email} joined the platform`,
        timestamp: user.createdAt,
        metadata: {
          userId: user._id,
          username: user.username,
          email: user.email
        }
      });
    });

    // Get recent community creations
    const recentCommunities = await Community.find({})
      .sort({ createdAt: -1 })
      .limit(5)
      .select("name slug admin createdAt")
      .populate("admin", "username email");

    recentCommunities.forEach(community => {
      activities.push({
        id: `community-${community._id}`,
        type: "community_creation",
        title: "New Community Created",
        description: `${community.name} was created by ${community.admin?.username || 'Unknown'}`,
        timestamp: community.createdAt,
        metadata: {
          communityId: community._id,
          communityName: community.name,
          communitySlug: community.slug,
          adminId: community.admin?._id,
          adminUsername: community.admin?.username
        }
      });
    });

    // Get recent course creations
    const recentCourses = await Course.find({})
      .sort({ createdAt: -1 })
      .limit(5)
      .select("title communityId createdAt")
      .populate("communityId", "name");

    recentCourses.forEach(course => {
      activities.push({
        id: `course-${course._id}`,
        type: "course_creation",
        title: "New Course Created",
        description: `${course.title} was created in ${course.communityId?.name || 'Unknown Community'}`,
        timestamp: course.createdAt,
        metadata: {
          courseId: course._id,
          courseTitle: course.title,
          communityId: course.communityId?._id,
          communityName: course.communityId?.name
        }
      });
    });

    // Get recent payments
    const recentPayments = await Payment.find({ status: "completed" })
      .sort({ createdAt: -1 })
      .limit(5)
      .select("amount currency userId createdAt")
      .populate("userId", "username email");

    recentPayments.forEach(payment => {
      activities.push({
        id: `payment-${payment._id}`,
        type: "payment_completed",
        title: "Payment Completed",
        description: `${payment.userId?.username || 'User'} made a payment of ${payment.amount} ${payment.currency}`,
        timestamp: payment.createdAt,
        metadata: {
          paymentId: payment._id,
          amount: payment.amount,
          currency: payment.currency,
          userId: payment.userId?._id,
          username: payment.userId?.username
        }
      });
    });

    // Get recent posts (if Post model exists)
    try {
      const recentPosts = await Post.find({})
        .sort({ createdAt: -1 })
        .limit(5)
        .select("title authorId communityId createdAt")
        .populate("authorId", "username")
        .populate("communityId", "name");

      recentPosts.forEach(post => {
        activities.push({
          id: `post-${post._id}`,
          type: "post_creation",
          title: "New Post Created",
          description: `${post.authorId?.username || 'User'} created "${post.title}" in ${post.communityId?.name || 'Unknown Community'}`,
          timestamp: post.createdAt,
          metadata: {
            postId: post._id,
            postTitle: post.title,
            authorId: post.authorId?._id,
            authorUsername: post.authorId?.username,
            communityId: post.communityId?._id,
            communityName: post.communityId?.name
          }
        });
      });
    } catch (error) {
      // Post model might not exist, skip this section
      console.log("Post model not found, skipping recent posts");
    }

    // Sort all activities by timestamp and limit
    const sortedActivities = activities
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, limit);

    // Add relative time formatting
    const activitiesWithRelativeTime = sortedActivities.map(activity => ({
      ...activity,
      relativeTime: getRelativeTime(activity.timestamp)
    }));

    return NextResponse.json({
      activities: activitiesWithRelativeTime,
      total: activitiesWithRelativeTime.length
    });

  } catch (error) {
    console.error("Error fetching recent activity:", error);
    return NextResponse.json(
      { error: "Failed to fetch recent activity" },
      { status: 500 }
    );
  }
}

function getRelativeTime(date: Date): string {
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - new Date(date).getTime()) / 1000);

  if (diffInSeconds < 60) {
    return "Just now";
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours} hour${hours > 1 ? 's' : ''} ago`;
  } else if (diffInSeconds < 604800) {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days} day${days > 1 ? 's' : ''} ago`;
  } else {
    return new Date(date).toLocaleDateString();
  }
}
