import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "@/lib/auth-helpers";
import { dbconnect } from "@/lib/db";
import { User } from "@/models/User";
import { Community } from "@/models/Community";
import { Course } from "@/models/Course";
import { Payment } from "@/models/Payment";
import { Post } from "@/models/Post";

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    await dbconnect();

    // Check if user is admin
    const user = await User.findById(session.user.id);
    if (!user || !user.isAdmin) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const period = searchParams.get("period") || "30d";

    // Calculate date range based on period
    const now = new Date();
    let startDate: Date;
    
    switch (period) {
      case "7d":
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case "30d":
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case "90d":
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      case "1y":
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }

    // Get comprehensive analytics data
    const analytics = await Promise.all([
      // User analytics
      User.countDocuments(),
      User.countDocuments({ createdAt: { $gte: startDate } }),
      User.countDocuments({ status: "active" }),
      User.countDocuments({ status: "suspended" }),
      User.countDocuments({ status: "banned" }),

      // Community analytics
      Community.countDocuments(),
      Community.countDocuments({ createdAt: { $gte: startDate } }),
      Community.countDocuments({ status: "active" }),
      Community.countDocuments({ status: "suspended" }),

      // Course analytics
      Course.countDocuments(),
      Course.countDocuments({ createdAt: { $gte: startDate } }),
      Course.countDocuments({ isPublished: true }),

      // Revenue analytics
      Payment.aggregate([
        { $match: { status: "completed" } },
        { $group: { _id: null, total: { $sum: "$amount" }, count: { $sum: 1 } } }
      ]),
      Payment.aggregate([
        { $match: { status: "completed", createdAt: { $gte: startDate } } },
        { $group: { _id: null, total: { $sum: "$amount" }, count: { $sum: 1 } } }
      ]),

      // Top communities by members
      Community.aggregate([
        { $project: { name: 1, memberCount: { $size: { $ifNull: ["$members", []] } } } },
        { $sort: { memberCount: -1 } },
        { $limit: 10 }
      ]),

      // User growth over time (last 12 months)
      User.aggregate([
        {
          $match: {
            createdAt: { $gte: new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000) }
          }
        },
        {
          $group: {
            _id: {
              year: { $year: "$createdAt" },
              month: { $month: "$createdAt" }
            },
            count: { $sum: 1 }
          }
        },
        { $sort: { "_id.year": 1, "_id.month": 1 } }
      ]),

      // Revenue over time (last 12 months)
      Payment.aggregate([
        {
          $match: {
            status: "completed",
            createdAt: { $gte: new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000) }
          }
        },
        {
          $group: {
            _id: {
              year: { $year: "$createdAt" },
              month: { $month: "$createdAt" }
            },
            revenue: { $sum: "$amount" },
            transactions: { $sum: 1 }
          }
        },
        { $sort: { "_id.year": 1, "_id.month": 1 } }
      ])
    ]);

    // Try to get post analytics (might not exist)
    let postAnalytics = { total: 0, period: 0 };
    try {
      const [totalPosts, periodPosts] = await Promise.all([
        Post.countDocuments(),
        Post.countDocuments({ createdAt: { $gte: startDate } })
      ]);
      postAnalytics = { total: totalPosts, period: periodPosts };
    } catch (error) {
      // Post model doesn't exist
    }

    const [
      totalUsers, newUsers, activeUsers, suspendedUsers, bannedUsers,
      totalCommunities, newCommunities, activeCommunities, suspendedCommunities,
      totalCourses, newCourses, publishedCourses,
      totalRevenue, periodRevenue,
      topCommunities, userGrowth, revenueGrowth
    ] = analytics;

    const analyticsData = {
      overview: {
        users: {
          total: totalUsers,
          new: newUsers,
          active: activeUsers,
          suspended: suspendedUsers,
          banned: bannedUsers
        },
        communities: {
          total: totalCommunities,
          new: newCommunities,
          active: activeCommunities,
          suspended: suspendedCommunities
        },
        courses: {
          total: totalCourses,
          new: newCourses,
          published: publishedCourses
        },
        posts: postAnalytics,
        revenue: {
          total: totalRevenue[0]?.total || 0,
          period: periodRevenue[0]?.total || 0,
          transactions: {
            total: totalRevenue[0]?.count || 0,
            period: periodRevenue[0]?.count || 0
          }
        }
      },
      charts: {
        userGrowth: userGrowth.map(item => ({
          month: `${item._id.year}-${String(item._id.month).padStart(2, '0')}`,
          users: item.count
        })),
        revenueGrowth: revenueGrowth.map(item => ({
          month: `${item._id.year}-${String(item._id.month).padStart(2, '0')}`,
          revenue: item.revenue,
          transactions: item.transactions
        }))
      },
      topCommunities: topCommunities.map(community => ({
        id: community._id,
        name: community.name,
        members: community.memberCount
      })),
      period
    };

    return NextResponse.json(analyticsData);

  } catch (error) {
    console.error("Error fetching admin analytics:", error);
    return NextResponse.json(
      { error: "Failed to fetch analytics" },
      { status: 500 }
    );
  }
}
