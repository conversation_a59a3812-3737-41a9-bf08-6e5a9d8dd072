import { useEffect, useCallback } from 'react';
import { useAppDispatch, useAppSelector } from './redux';
import { sessionManager } from '../services/sessionManager';
import { logout, checkAuthStatus } from '../store/slices/authSlice';

export const useSessionManager = () => {
  const dispatch = useAppDispatch();
  const { isAuthenticated, token } = useAppSelector((state) => state.auth);

  // Track user activity - simplified to avoid navigation issues
  useEffect(() => {
    if (isAuthenticated) {
      sessionManager.trackUserActivity();
    }
  }, [isAuthenticated]);

  // Check session status when app becomes active
  const checkSessionStatus = useCallback(async () => {
    if (!isAuthenticated || !token) {
      return;
    }

    try {
      // Check if session has expired due to inactivity
      const isSessionExpired = await sessionManager.isSessionExpired();
      if (isSessionExpired) {
        console.log('Session expired due to inactivity, logging out...');
        dispatch(logout());
        return;
      }

      // Check if token is expired or expiring soon
      const isTokenExpired = await sessionManager.isTokenExpired(token);
      if (isTokenExpired) {
        console.log('Token expired, checking auth status...');
        dispatch(checkAuthStatus());
        return;
      }

      const isTokenExpiringSoon = await sessionManager.isTokenExpiringSoon(token);
      if (isTokenExpiringSoon) {
        console.log('Token expiring soon, refreshing...');
        dispatch(checkAuthStatus());
      }
    } catch (error) {
      console.error('Error checking session status:', error);
    }
  }, [isAuthenticated, token, dispatch]);

  // Initialize session manager
  useEffect(() => {
    if (isAuthenticated) {
      // Start session tracking
      sessionManager.trackUserActivity();
      
      // Check session status immediately
      checkSessionStatus();
    }

    return () => {
      // Cleanup is handled by the singleton session manager
    };
  }, [isAuthenticated, checkSessionStatus]);

  // Provide methods to manually track activity
  const trackActivity = useCallback(() => {
    if (isAuthenticated) {
      sessionManager.trackUserActivity();
    }
  }, [isAuthenticated]);

  const forceSessionCheck = useCallback(() => {
    checkSessionStatus();
  }, [checkSessionStatus]);

  return {
    trackActivity,
    forceSessionCheck,
    sessionConfig: sessionManager.getSessionConfig(),
  };
};

// Hook for tracking user interactions
export const useActivityTracker = () => {
  const { isAuthenticated } = useAppSelector((state) => state.auth);

  const trackActivity = useCallback(() => {
    if (isAuthenticated) {
      sessionManager.trackUserActivity();
    }
  }, [isAuthenticated]);

  return { trackActivity };
};
