#!/usr/bin/env node

/**
 * Database Connection Test Script
 * 
 * This script tests the MongoDB connection and verifies that
 * user registration and authentication work properly.
 */

import dotenv from 'dotenv';
import { connectDB } from '../config/database.js';
import { User } from '../models/User.js';

// Load environment variables
dotenv.config();

async function testDatabaseConnection() {
  console.log('🔄 Testing database connection...\n');

  try {
    // Test 1: Connect to database
    console.log('1️⃣ Testing MongoDB connection...');
    await connectDB();
    console.log('✅ Database connection successful\n');

    // Test 2: Test user creation
    console.log('2️⃣ Testing user creation...');
    const testUser = {
      username: 'testuser_' + Date.now(),
      email: 'test_' + Date.now() + '@example.com',
      password: 'TestPassword123!',
      firstName: 'Test',
      lastName: 'User',
      name: 'Test User'
    };

    const user = await User.create(testUser);
    console.log('✅ User created successfully:', {
      id: user._id,
      username: user.username,
      email: user.email,
      name: user.name,
      passwordHashed: user.password.startsWith('$2b$')
    });

    // Test 3: Test user lookup
    console.log('\n3️⃣ Testing user lookup...');
    const foundUser = await User.findOne({ email: testUser.email });
    if (foundUser) {
      console.log('✅ User lookup successful:', {
        id: foundUser._id,
        username: foundUser.username,
        email: foundUser.email
      });
    } else {
      throw new Error('User not found after creation');
    }

    // Test 4: Test password verification
    console.log('\n4️⃣ Testing password verification...');
    const isPasswordValid = await foundUser.matchPassword(testUser.password);
    if (isPasswordValid) {
      console.log('✅ Password verification successful');
    } else {
      throw new Error('Password verification failed');
    }

    // Test 5: Test duplicate user prevention
    console.log('\n5️⃣ Testing duplicate user prevention...');
    try {
      await User.create(testUser);
      throw new Error('Duplicate user creation should have failed');
    } catch (error) {
      if (error.code === 11000) {
        console.log('✅ Duplicate user prevention working correctly');
      } else {
        throw error;
      }
    }

    // Test 6: Test user count
    console.log('\n6️⃣ Testing user count...');
    const userCount = await User.countDocuments();
    console.log(`✅ Total users in database: ${userCount}`);

    // Cleanup: Remove test user
    console.log('\n🧹 Cleaning up test data...');
    await User.deleteOne({ _id: user._id });
    console.log('✅ Test user removed');

    console.log('\n🎉 All database tests passed successfully!');
    console.log('\n📊 Test Summary:');
    console.log('   ✅ Database connection');
    console.log('   ✅ User creation');
    console.log('   ✅ Password hashing');
    console.log('   ✅ User lookup');
    console.log('   ✅ Password verification');
    console.log('   ✅ Duplicate prevention');
    console.log('   ✅ Data cleanup');

  } catch (error) {
    console.error('❌ Database test failed:', error.message);
    console.error('\n🔍 Error details:', error);
    
    if (error.message.includes('MONGODB_URI')) {
      console.log('\n💡 Make sure MONGODB_URI is set in your .env file');
    }
    
    if (error.message.includes('authentication failed')) {
      console.log('\n💡 Check your MongoDB credentials');
    }
    
    if (error.message.includes('network')) {
      console.log('\n💡 Check your internet connection and MongoDB Atlas network access');
    }

    process.exit(1);
  }
}

async function testRegistrationAPI() {
  console.log('\n🔄 Testing registration API simulation...\n');

  try {
    // Simulate registration data validation
    const timestamp = Date.now().toString().slice(-8); // Use last 8 digits to keep username short
    const registrationData = {
      username: 'api_' + timestamp,
      email: 'apitest_' + Date.now() + '@example.com',
      password: 'ApiTestPassword123!',
      firstName: 'API',
      lastName: 'Test'
    };

    console.log('📝 Testing registration data validation...');
    
    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(registrationData.email)) {
      throw new Error('Email validation failed');
    }
    console.log('✅ Email validation passed');

    // Password strength validation
    const hasUpperCase = /[A-Z]/.test(registrationData.password);
    const hasLowerCase = /[a-z]/.test(registrationData.password);
    const hasNumbers = /\d/.test(registrationData.password);
    const hasSpecialChar = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(registrationData.password);

    if (!hasUpperCase || !hasLowerCase || !hasNumbers || !hasSpecialChar) {
      throw new Error('Password strength validation failed');
    }
    console.log('✅ Password strength validation passed');

    // Username validation
    if (registrationData.username.length < 3 || registrationData.username.length > 20) {
      throw new Error('Username length validation failed');
    }
    
    if (!/^[a-zA-Z0-9_]+$/.test(registrationData.username)) {
      throw new Error('Username character validation failed');
    }
    console.log('✅ Username validation passed');

    // Test user creation through API simulation
    console.log('\n📝 Testing user creation through API simulation...');
    const user = await User.create({
      username: registrationData.username.toLowerCase(),
      email: registrationData.email.toLowerCase(),
      password: registrationData.password,
      firstName: registrationData.firstName,
      lastName: registrationData.lastName,
      name: `${registrationData.firstName} ${registrationData.lastName}`,
      isEmailVerified: false,
      emailVerified: false,
      role: 'user'
    });

    console.log('✅ API simulation user created:', {
      id: user._id,
      username: user.username,
      email: user.email,
      name: user.name,
      role: user.role,
      isEmailVerified: user.isEmailVerified
    });

    // Cleanup
    await User.deleteOne({ _id: user._id });
    console.log('✅ API test user cleaned up');

    console.log('\n🎉 Registration API simulation passed!');

  } catch (error) {
    console.error('❌ Registration API test failed:', error.message);
    throw error;
  }
}

// Run tests
async function runAllTests() {
  try {
    await testDatabaseConnection();
    await testRegistrationAPI();
    
    console.log('\n🏆 ALL TESTS PASSED! Your database is ready for user registration.');
    process.exit(0);
  } catch (error) {
    console.error('\n💥 TESTS FAILED! Please fix the issues before proceeding.');
    process.exit(1);
  }
}

runAllTests();
