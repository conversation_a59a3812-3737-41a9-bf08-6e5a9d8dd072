import React from 'react';
import { render, waitFor } from '@testing-library/react-native';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { Text } from 'react-native';
import AuthGuard from '../../components/AuthGuard';
import authReducer from '../../store/slices/authSlice';

// Mock navigation
const mockNavigate = jest.fn();
const mockReplace = jest.fn();

jest.mock('@react-navigation/native', () => ({
  useNavigation: () => ({
    navigate: mockNavigate,
    replace: mockReplace,
  }),
}));

// Mock session manager
jest.mock('../../services/sessionManager', () => ({
  sessionManager: {
    isSessionExpired: jest.fn(),
    trackUserActivity: jest.fn(),
  },
}));

// Mock security service
jest.mock('../../services/security', () => ({
  securityService: {
    isAccountLocked: jest.fn(),
  },
}));

describe('AuthGuard Component', () => {
  let store: any;

  const TestComponent = () => <Text testID="protected-content">Protected Content</Text>;

  beforeEach(() => {
    jest.clearAllMocks();
    store = configureStore({
      reducer: {
        auth: authReducer,
      },
    });
  });

  const renderWithProvider = (component: React.ReactElement, initialState?: any) => {
    if (initialState) {
      store = configureStore({
        reducer: { auth: authReducer },
        preloadedState: { auth: initialState },
      });
    }

    return render(
      <Provider store={store}>
        {component}
      </Provider>
    );
  };

  describe('Authentication States', () => {
    it('should render children when user is authenticated', () => {
      const authenticatedState = {
        user: { id: '1', username: 'testuser', email: '<EMAIL>' },
        token: 'valid-token',
        isAuthenticated: true,
        isLoading: false,
        error: null,
      };

      const { getByTestId } = renderWithProvider(
        <AuthGuard>
          <TestComponent />
        </AuthGuard>,
        authenticatedState
      );

      expect(getByTestId('protected-content')).toBeTruthy();
    });

    it('should not render children when user is not authenticated', () => {
      const unauthenticatedState = {
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      };

      const { queryByTestId } = renderWithProvider(
        <AuthGuard>
          <TestComponent />
        </AuthGuard>,
        unauthenticatedState
      );

      expect(queryByTestId('protected-content')).toBeNull();
    });

    it('should show loading state when authentication is in progress', () => {
      const loadingState = {
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: true,
        error: null,
      };

      const { getByTestId } = renderWithProvider(
        <AuthGuard>
          <TestComponent />
        </AuthGuard>,
        loadingState
      );

      expect(getByTestId('auth-loading')).toBeTruthy();
    });
  });

  describe('Navigation Behavior', () => {
    it('should navigate to login when user is not authenticated', async () => {
      const unauthenticatedState = {
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      };

      renderWithProvider(
        <AuthGuard>
          <TestComponent />
        </AuthGuard>,
        unauthenticatedState
      );

      await waitFor(() => {
        expect(mockReplace).toHaveBeenCalledWith('Login');
      });
    });

    it('should not navigate when user is authenticated', () => {
      const authenticatedState = {
        user: { id: '1', username: 'testuser', email: '<EMAIL>' },
        token: 'valid-token',
        isAuthenticated: true,
        isLoading: false,
        error: null,
      };

      renderWithProvider(
        <AuthGuard>
          <TestComponent />
        </AuthGuard>,
        authenticatedState
      );

      expect(mockReplace).not.toHaveBeenCalled();
      expect(mockNavigate).not.toHaveBeenCalled();
    });
  });

  describe('Custom Redirect', () => {
    it('should navigate to custom redirect route when specified', async () => {
      const unauthenticatedState = {
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      };

      renderWithProvider(
        <AuthGuard redirectTo="CustomLogin">
          <TestComponent />
        </AuthGuard>,
        unauthenticatedState
      );

      await waitFor(() => {
        expect(mockReplace).toHaveBeenCalledWith('CustomLogin');
      });
    });
  });

  describe('Loading States', () => {
    it('should show custom loading component when provided', () => {
      const loadingState = {
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: true,
        error: null,
      };

      const CustomLoader = () => <Text testID="custom-loader">Custom Loading...</Text>;

      const { getByTestId } = renderWithProvider(
        <AuthGuard loadingComponent={<CustomLoader />}>
          <TestComponent />
        </AuthGuard>,
        loadingState
      );

      expect(getByTestId('custom-loader')).toBeTruthy();
    });

    it('should show default loading when no custom loading component provided', () => {
      const loadingState = {
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: true,
        error: null,
      };

      const { getByTestId } = renderWithProvider(
        <AuthGuard>
          <TestComponent />
        </AuthGuard>,
        loadingState
      );

      expect(getByTestId('auth-loading')).toBeTruthy();
    });
  });

  describe('Error Handling', () => {
    it('should handle authentication errors gracefully', () => {
      const errorState = {
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        error: 'Authentication failed',
      };

      const { queryByTestId } = renderWithProvider(
        <AuthGuard>
          <TestComponent />
        </AuthGuard>,
        errorState
      );

      // Should not render protected content on error
      expect(queryByTestId('protected-content')).toBeNull();
    });
  });
});
