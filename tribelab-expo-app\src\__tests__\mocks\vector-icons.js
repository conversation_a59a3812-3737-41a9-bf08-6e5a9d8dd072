// Mock for Vector Icons
import React from 'react';

// Create a mock icon component that accepts all props
const MockIcon = React.forwardRef((props, ref) => {
  return React.createElement('Text', {
    ...props,
    ref,
    testID: props.testID || 'mock-icon',
    children: props.name || 'icon'
  });
});

MockIcon.displayName = 'MockIcon';

// Export the mock for direct imports
module.exports = MockIcon;

// Mock all react-native-vector-icons libraries
jest.mock('react-native-vector-icons/MaterialIcons', () => MockIcon);
jest.mock('react-native-vector-icons/MaterialCommunityIcons', () => MockIcon);
jest.mock('react-native-vector-icons/Ionicons', () => MockIcon);
jest.mock('react-native-vector-icons/FontAwesome', () => MockIcon);
jest.mock('react-native-vector-icons/FontAwesome5', () => MockIcon);
jest.mock('react-native-vector-icons/Feather', () => MockIcon);
jest.mock('react-native-vector-icons/AntDesign', () => MockIcon);
jest.mock('react-native-vector-icons/Entypo', () => MockIcon);
jest.mock('react-native-vector-icons/EvilIcons', () => MockIcon);
jest.mock('react-native-vector-icons/Foundation', () => MockIcon);
jest.mock('react-native-vector-icons/Octicons', () => MockIcon);
jest.mock('react-native-vector-icons/SimpleLineIcons', () => MockIcon);
jest.mock('react-native-vector-icons/Zocial', () => MockIcon);

// Mock Expo Vector Icons
jest.mock('@expo/vector-icons', () => ({
  MaterialIcons: MockIcon,
  MaterialCommunityIcons: MockIcon,
  Ionicons: MockIcon,
  FontAwesome: MockIcon,
  FontAwesome5: MockIcon,
  Feather: MockIcon,
  AntDesign: MockIcon,
  Entypo: MockIcon,
  EvilIcons: MockIcon,
  Foundation: MockIcon,
  Octicons: MockIcon,
  SimpleLineIcons: MockIcon,
  Zocial: MockIcon,
}));
