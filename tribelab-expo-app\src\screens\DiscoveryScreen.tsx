import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  FlatList,
  RefreshControl,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/Ionicons';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { fetchTrendingContent, fetchRecommendedContent } from '../store/slices/discoverySlice';
import Toast from 'react-native-toast-message';
import { getSafeImageSource } from '../utils/imageUtils';

const { width: screenWidth } = Dimensions.get('window');

interface DiscoveryItem {
  id: string;
  type: 'community' | 'course' | 'user' | 'post';
  title: string;
  subtitle?: string;
  description?: string;
  image?: string;
  avatar?: string;
  metadata?: {
    category?: string;
    memberCount?: number;
    rating?: number;
    price?: number;
    duration?: string;
    author?: string;
    tags?: string[];
  };
  trending?: boolean;
  featured?: boolean;
  recommended?: boolean;
}

const DiscoveryScreen = () => {
  const navigation = useNavigation();
  const dispatch = useAppDispatch();
  
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeCategory, setActiveCategory] = useState('all');
  const [discoveryItems, setDiscoveryItems] = useState<DiscoveryItem[]>([]);

  const categories = [
    { key: 'all', label: 'All', icon: 'grid' },
    { key: 'trending', label: 'Trending', icon: 'trending-up' },
    { key: 'communities', label: 'Communities', icon: 'people' },
    { key: 'courses', label: 'Courses', icon: 'school' },
    { key: 'creators', label: 'Creators', icon: 'person' },
  ];

  useEffect(() => {
    loadDiscoveryContent();
  }, [activeCategory]);

  const loadDiscoveryContent = async (refresh = false) => {
    try {
      if (refresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      // Mock discovery data - replace with real API calls
      const mockItems: DiscoveryItem[] = [
        {
          id: '1',
          type: 'community',
          title: 'React Native Developers',
          subtitle: 'Technology Community',
          description: 'Join 15K+ developers building amazing mobile apps with React Native',
          image: undefined, // Will use fallback
          avatar: undefined, // Will use fallback
          metadata: {
            category: 'Technology',
            memberCount: 15420,
            tags: ['React Native', 'Mobile Development', 'JavaScript'],
          },
          trending: true,
          featured: true,
        },
        {
          id: '2',
          type: 'course',
          title: 'Complete React Native Course',
          subtitle: 'Mobile Development',
          description: 'Learn to build iOS and Android apps from scratch',
          image: undefined, // Will use fallback
          metadata: {
            category: 'Technology',
            rating: 4.8,
            price: 99,
            duration: '12 hours',
            author: 'John Smith',
            tags: ['React Native', 'Mobile Apps', 'Cross-platform'],
          },
          recommended: true,
        },
        {
          id: '3',
          type: 'user',
          title: 'Sarah Johnson',
          subtitle: 'UI/UX Designer',
          description: 'Senior designer at Google, sharing design tips and insights',
          avatar: undefined, // Will use fallback
          metadata: {
            category: 'Design',
            tags: ['UI Design', 'UX Research', 'Figma'],
          },
          featured: true,
        },
        {
          id: '4',
          type: 'community',
          title: 'Startup Founders Network',
          subtitle: 'Entrepreneurship',
          description: 'Connect with fellow entrepreneurs and grow your startup',
          image: undefined, // Will use fallback
          avatar: undefined, // Will use fallback
          metadata: {
            category: 'Business',
            memberCount: 8750,
            tags: ['Startup', 'Entrepreneurship', 'Networking'],
          },
          trending: true,
        },
        {
          id: '5',
          type: 'course',
          title: 'Digital Marketing Mastery',
          subtitle: 'Marketing Strategy',
          description: 'Master digital marketing and grow your business online',
          image: 'https://via.placeholder.com/300x200',
          metadata: {
            category: 'Marketing',
            rating: 4.6,
            price: 79,
            duration: '8 hours',
            author: 'Marketing Pro',
            tags: ['Digital Marketing', 'SEO', 'Social Media'],
          },
        },
      ];

      // Filter based on active category
      let filteredItems = mockItems;
      if (activeCategory !== 'all') {
        if (activeCategory === 'trending') {
          filteredItems = mockItems.filter(item => item.trending);
        } else if (activeCategory === 'communities') {
          filteredItems = mockItems.filter(item => item.type === 'community');
        } else if (activeCategory === 'courses') {
          filteredItems = mockItems.filter(item => item.type === 'course');
        } else if (activeCategory === 'creators') {
          filteredItems = mockItems.filter(item => item.type === 'user');
        }
      }

      setDiscoveryItems(filteredItems);
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to load discovery content',
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleItemPress = (item: DiscoveryItem) => {
    switch (item.type) {
      case 'community':
        navigation.navigate('Community', { communityId: item.id });
        break;
      case 'course':
        navigation.navigate('CourseDetail', { courseId: item.id });
        break;
      case 'user':
        navigation.navigate('UserProfile', { userId: item.id });
        break;
      case 'post':
        navigation.navigate('PostDetail', { postId: item.id });
        break;
    }
  };

  const renderCategoryTabs = () => (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      style={styles.categoryTabs}
      contentContainerStyle={styles.categoryTabsContent}
    >
      {categories.map((category) => (
        <TouchableOpacity
          key={category.key}
          style={[
            styles.categoryTab,
            activeCategory === category.key && styles.activeCategoryTab
          ]}
          onPress={() => setActiveCategory(category.key)}
        >
          <Icon
            name={category.icon}
            size={20}
            color={activeCategory === category.key ? '#fff' : '#666'}
          />
          <Text style={[
            styles.categoryTabText,
            activeCategory === category.key && styles.activeCategoryTabText
          ]}>
            {category.label}
          </Text>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );

  const renderFeaturedItem = (item: DiscoveryItem) => (
    <TouchableOpacity
      style={styles.featuredCard}
      onPress={() => handleItemPress(item)}
      activeOpacity={0.8}
    >
      <Image
        source={getSafeImageSource(item.image || item.avatar, item.title, 'banner')}
        style={styles.featuredImage}
      />
      <View style={styles.featuredOverlay}>
        <View style={styles.featuredBadges}>
          {item.featured && (
            <View style={styles.featuredBadge}>
              <Text style={styles.featuredBadgeText}>Featured</Text>
            </View>
          )}
          {item.trending && (
            <View style={styles.trendingBadge}>
              <Icon name="trending-up" size={12} color="#fff" />
              <Text style={styles.trendingBadgeText}>Trending</Text>
            </View>
          )}
        </View>
        <View style={styles.featuredContent}>
          <Text style={styles.featuredTitle}>{item.title}</Text>
          <Text style={styles.featuredSubtitle}>{item.subtitle}</Text>
          <Text style={styles.featuredDescription} numberOfLines={2}>
            {item.description}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderDiscoveryItem = ({ item }: { item: DiscoveryItem }) => (
    <TouchableOpacity
      style={styles.discoveryCard}
      onPress={() => handleItemPress(item)}
      activeOpacity={0.7}
    >
      <Image
        source={getSafeImageSource(item.image || item.avatar, item.title, 'thumbnail')}
        style={styles.discoveryImage}
      />
      <View style={styles.discoveryContent}>
        <View style={styles.discoveryHeader}>
          <Text style={styles.discoveryTitle} numberOfLines={1}>
            {item.title}
          </Text>
          {item.metadata?.rating && (
            <View style={styles.ratingContainer}>
              <Icon name="star" size={14} color="#FFD700" />
              <Text style={styles.ratingText}>{item.metadata.rating}</Text>
            </View>
          )}
        </View>
        
        <Text style={styles.discoverySubtitle} numberOfLines={1}>
          {item.subtitle}
        </Text>
        
        <Text style={styles.discoveryDescription} numberOfLines={2}>
          {item.description}
        </Text>
        
        <View style={styles.discoveryMeta}>
          {item.metadata?.memberCount && (
            <Text style={styles.metaText}>
              {item.metadata.memberCount.toLocaleString()} members
            </Text>
          )}
          {item.metadata?.price && (
            <Text style={styles.priceText}>
              ${item.metadata.price}
            </Text>
          )}
          {item.metadata?.duration && (
            <Text style={styles.metaText}>
              {item.metadata.duration}
            </Text>
          )}
        </View>
        
        {item.metadata?.tags && (
          <View style={styles.tagsContainer}>
            {item.metadata.tags.slice(0, 2).map((tag, index) => (
              <View key={index} style={styles.tag}>
                <Text style={styles.tagText}>{tag}</Text>
              </View>
            ))}
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  const featuredItems = discoveryItems.filter(item => item.featured);
  const regularItems = discoveryItems.filter(item => !item.featured);

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Discover</Text>
        <TouchableOpacity
          style={styles.searchButton}
          onPress={() => navigation.navigate('Search')}
        >
          <Icon name="search" size={24} color="#007AFF" />
        </TouchableOpacity>
      </View>

      {/* Category Tabs */}
      {renderCategoryTabs()}

      {/* Content */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Discovering content...</Text>
        </View>
      ) : (
        <ScrollView
          style={styles.content}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={() => loadDiscoveryContent(true)} />
          }
          showsVerticalScrollIndicator={false}
        >
          {/* Featured Section */}
          {featuredItems.length > 0 && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Featured</Text>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.featuredContainer}
              >
                {featuredItems.map(renderFeaturedItem)}
              </ScrollView>
            </View>
          )}

          {/* Discovery Items */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>
              {activeCategory === 'all' ? 'Recommended for You' : categories.find(c => c.key === activeCategory)?.label}
            </Text>
            <FlatList
              data={regularItems}
              renderItem={renderDiscoveryItem}
              keyExtractor={(item) => item.id}
              scrollEnabled={false}
              contentContainerStyle={styles.discoveryList}
              ListEmptyComponent={
                <View style={styles.emptyContainer}>
                  <Icon name="compass-outline" size={64} color="#ccc" />
                  <Text style={styles.emptyTitle}>Nothing to Discover</Text>
                  <Text style={styles.emptyText}>
                    Check back later for new content recommendations
                  </Text>
                </View>
              }
            />
          </View>
        </ScrollView>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  searchButton: {
    padding: 8,
  },
  categoryTabs: {
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  categoryTabsContent: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    gap: 12,
  },
  categoryTab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    gap: 6,
  },
  activeCategoryTab: {
    backgroundColor: '#007AFF',
  },
  categoryTabText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  activeCategoryTabText: {
    color: '#fff',
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
    paddingHorizontal: 20,
  },
  featuredContainer: {
    paddingHorizontal: 20,
    gap: 16,
  },
  featuredCard: {
    width: screenWidth * 0.8,
    height: 200,
    borderRadius: 16,
    overflow: 'hidden',
    position: 'relative',
  },
  featuredImage: {
    width: '100%',
    height: '100%',
  },
  featuredOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    padding: 16,
    justifyContent: 'space-between',
  },
  featuredBadges: {
    flexDirection: 'row',
    gap: 8,
  },
  featuredBadge: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  featuredBadgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  trendingBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FF6B6B',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  trendingBadgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  featuredContent: {
    // No additional styles needed
  },
  featuredTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 4,
  },
  featuredSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: 8,
  },
  featuredDescription: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    lineHeight: 18,
  },
  discoveryList: {
    paddingHorizontal: 20,
  },
  discoveryCard: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 12,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#eee',
  },
  discoveryImage: {
    width: 100,
    height: 100,
  },
  discoveryContent: {
    flex: 1,
    padding: 12,
  },
  discoveryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  discoveryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    flex: 1,
    marginRight: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  ratingText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  discoverySubtitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  discoveryDescription: {
    fontSize: 14,
    color: '#999',
    lineHeight: 18,
    marginBottom: 8,
  },
  discoveryMeta: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 8,
  },
  metaText: {
    fontSize: 12,
    color: '#999',
  },
  priceText: {
    fontSize: 12,
    color: '#007AFF',
    fontWeight: '600',
  },
  tagsContainer: {
    flexDirection: 'row',
    gap: 6,
  },
  tag: {
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
  },
  tagText: {
    fontSize: 10,
    color: '#666',
    fontWeight: '500',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 22,
  },
});

export default DiscoveryScreen;
