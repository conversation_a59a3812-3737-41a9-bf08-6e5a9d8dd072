import "react-native-gesture-handler";
import React, { useEffect } from "react";
import { SafeAreaProvider } from "react-native-safe-area-context";
import { StatusBar } from "expo-status-bar";
import Toast from "react-native-toast-message";
import ReduxProvider from "./providers/ReduxProvider";
import NotificationProvider from "./providers/NotificationProvider";
import AppNavigator from "./navigation/AppNavigator";
import ErrorBoundary from "./components/ErrorBoundary";
import notificationService from "./services/notifications";

const App = () => {
  useEffect(() => {
    // Initialize notification service - disabled for development
    // notificationService.initialize();
  }, []);

  return (
    <ErrorBoundary>
      <ReduxProvider>
        <SafeAreaProvider>
          <StatusBar style="auto" />
          <AppNavigator />
          <Toast />
        </SafeAreaProvider>
      </ReduxProvider>
    </ErrorBoundary>
  );
};

export default App;
