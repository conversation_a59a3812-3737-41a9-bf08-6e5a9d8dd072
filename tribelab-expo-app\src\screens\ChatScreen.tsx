import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  TextInput,
  RefreshControl,
  ActivityIndicator,
  Alert,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useNavigation } from "@react-navigation/native";
import Icon from "react-native-vector-icons/Ionicons";
import { useAppDispatch, useAppSelector } from "../hooks/redux";
import { fetchChats, setCurrentChat } from "../store/slices/chatSlice";
import webSocketService from "../services/websocket";
import Toast from "react-native-toast-message";

const ChatScreen = () => {
  const navigation = useNavigation();
  const dispatch = useAppDispatch();
  const { chats, isLoading, pagination } = useAppSelector(
    (state) => state.chat
  );
  const { user } = useAppSelector((state) => state.auth);

  const [searchQuery, setSearchQuery] = useState("");
  const [refreshing, setRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);

  useEffect(() => {
    loadChats();

    // Connect to WebSocket for real-time updates
    if (user) {
      webSocketService.connect();
    }
  }, [user]);

  const loadChats = async (refresh = false) => {
    try {
      if (refresh) {
        setRefreshing(true);
      }
      await dispatch(fetchChats({ page: 1 })).unwrap();
    } catch (error) {
      Toast.show({
        type: "error",
        text1: "Error",
        text2: "Failed to load chats",
      });
    } finally {
      setRefreshing(false);
    }
  };

  const loadMoreChats = async () => {
    if (loadingMore || !pagination.chats.hasMore) return;

    try {
      setLoadingMore(true);
      await dispatch(fetchChats({ page: pagination.chats.page })).unwrap();
    } catch (error) {
      Toast.show({
        type: "error",
        text1: "Error",
        text2: "Failed to load more chats",
      });
    } finally {
      setLoadingMore(false);
    }
  };

  const handleChatPress = (chat: any) => {
    dispatch(setCurrentChat(chat));
    navigation.navigate("ChatDetail", { chatId: chat.id });
  };

  const handleNewChat = () => {
    navigation.navigate("NewChat");
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString("en-US", {
        hour: "numeric",
        minute: "2-digit",
        hour12: true,
      });
    } else if (diffInHours < 168) {
      // 7 days
      return date.toLocaleDateString("en-US", { weekday: "short" });
    } else {
      return date.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      });
    }
  };

  const getOtherParticipant = (chat: any) => {
    if (chat.type === "direct") {
      return chat.participants.find((p: any) => p.id !== user?.id);
    }
    return null;
  };

  const getChatDisplayName = (chat: any) => {
    if (chat.type === "group" || chat.type === "community") {
      return chat.name;
    }
    const otherParticipant = getOtherParticipant(chat);
    return otherParticipant?.name || "Unknown User";
  };

  const getChatAvatar = (chat: any) => {
    if (chat.avatar) {
      return chat.avatar;
    }
    if (chat.type === "direct") {
      const otherParticipant = getOtherParticipant(chat);
      return otherParticipant?.avatar;
    }
    return null;
  };

  const filteredChats = chats.filter((chat) => {
    if (!searchQuery) return true;
    const displayName = getChatDisplayName(chat).toLowerCase();
    return displayName.includes(searchQuery.toLowerCase());
  });

  const renderChatItem = ({ item: chat }: { item: any }) => {
    const displayName = getChatDisplayName(chat);
    const avatar = getChatAvatar(chat);
    const lastMessage = chat.lastMessage?.content || "No messages yet";
    const time = chat.lastMessage ? formatTime(chat.lastMessage.createdAt) : "";
    const isUnread = chat.unreadCount > 0;
    const otherParticipant = getOtherParticipant(chat);
    const isOnline =
      chat.type === "direct" ? otherParticipant?.isOnline : false;

    return (
      <TouchableOpacity
        style={styles.chatItem}
        onPress={() => handleChatPress(chat)}
        activeOpacity={0.7}
      >
        <View style={styles.avatarContainer}>
          {avatar ? (
            <Image
              source={{ uri: avatar }}
              style={styles.avatar}
            />
          ) : (
            <View style={[styles.avatar, styles.defaultAvatar]}>
              <Text style={styles.defaultAvatarText}>
                {displayName.charAt(0).toUpperCase()}
              </Text>
            </View>
          )}
          {isOnline && <View style={styles.onlineIndicator} />}
        </View>

        <View style={styles.chatContent}>
          <View style={styles.chatHeader}>
            <Text style={styles.chatName} numberOfLines={1}>
              {displayName}
            </Text>
            <Text style={styles.chatTime}>{time}</Text>
          </View>

          <View style={styles.chatFooter}>
            <Text
              style={[styles.chatMessage, isUnread && styles.unreadMessage]}
              numberOfLines={1}
            >
              {chat.lastMessage?.sender?.name && chat.type !== "direct"
                ? `${chat.lastMessage.sender.name}: ${lastMessage}`
                : lastMessage}
            </Text>
            {isUnread && (
              <View style={styles.unreadBadge}>
                <Text style={styles.unreadBadgeText}>
                  {chat.unreadCount > 99 ? "99+" : chat.unreadCount.toString()}
                </Text>
              </View>
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const displayChats = filteredChats;

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Chats</Text>
        <TouchableOpacity style={styles.headerButton} onPress={handleNewChat}>
          <Icon name="add" size={24} color="#007AFF" />
        </TouchableOpacity>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Icon name="search" size={20} color="#999" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search chats"
          placeholderTextColor="#999"
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      {/* Chat List */}
      {isLoading && displayChats.length === 0 ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Loading chats...</Text>
        </View>
      ) : (
        <FlatList
          data={displayChats}
          renderItem={renderChatItem}
          keyExtractor={(item) => item.id}
          style={styles.chatList}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={() => loadChats(true)}
            />
          }
          onEndReached={loadMoreChats}
          onEndReachedThreshold={0.1}
          ListFooterComponent={
            loadingMore ? (
              <View style={styles.loadMoreContainer}>
                <ActivityIndicator size="small" color="#007AFF" />
              </View>
            ) : null
          }
          ListEmptyComponent={
            !isLoading ? (
              <View style={styles.emptyContainer}>
                <Icon name="chatbubbles-outline" size={64} color="#ccc" />
                <Text style={styles.emptyTitle}>No Chats Yet</Text>
                <Text style={styles.emptyText}>
                  Start a conversation with someone
                </Text>
                <TouchableOpacity
                  style={styles.startChatButton}
                  onPress={handleNewChat}
                >
                  <Icon name="add" size={20} color="#fff" />
                  <Text style={styles.startChatButtonText}>Start New Chat</Text>
                </TouchableOpacity>
              </View>
            ) : null
          }
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 15,
    paddingTop: 50,
    paddingBottom: 10,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: "bold",
  },
  headerButton: {
    padding: 5,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f2f2f2",
    borderRadius: 25,
    marginHorizontal: 15,
    marginVertical: 10,
    paddingHorizontal: 15,
    paddingVertical: 10,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: "#000",
  },
  chatList: {
    flex: 1,
  },
  chatItem: {
    flexDirection: "row",
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 15,
  },
  defaultAvatar: {
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  defaultAvatarText: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  chatContent: {
    flex: 1,
    justifyContent: "center",
  },
  chatHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 5,
  },
  chatName: {
    fontSize: 16,
    fontWeight: "500",
  },
  chatTime: {
    fontSize: 14,
    color: "#999",
  },
  chatFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  chatMessage: {
    fontSize: 14,
    color: "#999",
    flex: 1,
    marginRight: 10,
  },
  unreadMessage: {
    color: "#000",
    fontWeight: "500",
  },
  unreadBadge: {
    backgroundColor: "#4080ff",
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: "center",
    alignItems: "center",
  },
  unreadBadgeText: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "bold",
  },
  readBadge: {
    width: 20,
    height: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 60,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: "#666",
  },
  loadMoreContainer: {
    paddingVertical: 20,
    alignItems: "center",
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#333",
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
    marginBottom: 24,
  },
  startChatButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#007AFF",
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 25,
    gap: 8,
  },
  startChatButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  avatarContainer: {
    position: "relative",
    marginRight: 16,
  },
  onlineIndicator: {
    position: "absolute",
    bottom: 2,
    right: 2,
    width: 14,
    height: 14,
    borderRadius: 7,
    backgroundColor: "#34C759",
    borderWidth: 2,
    borderColor: "#fff",
  },
});

export default ChatScreen;
