/**
 * Utility functions for handling images and generating fallbacks
 */

/**
 * Generate a color based on a string (like name or ID)
 */
export const generateColorFromString = (str: string): string => {
  const colors = [
    '#007AFF', // Blue
    '#34C759', // Green
    '#FF9500', // Orange
    '#FF3B30', // Red
    '#AF52DE', // Purple
    '#FF2D92', // Pink
    '#5AC8FA', // Light Blue
    '#FFCC00', // Yellow
    '#FF6B6B', // Light Red
    '#4ECDC4', // Teal
    '#45B7D1', // Sky Blue
    '#96CEB4', // Mint Green
    '#FFEAA7', // Light Yellow
    '#DDA0DD', // Plum
    '#98D8C8', // Mint
  ];
  
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash);
  }
  
  return colors[Math.abs(hash) % colors.length];
};

/**
 * Get initials from a name
 */
export const getInitials = (name: string): string => {
  if (!name) return '?';
  
  const words = name.trim().split(' ');
  if (words.length === 1) {
    return words[0].charAt(0).toUpperCase();
  }
  
  return (words[0].charAt(0) + words[words.length - 1].charAt(0)).toUpperCase();
};

/**
 * Generate a data URI for a colored circle with initials
 */
export const generateAvatarDataUri = (name: string, size: number = 100): string => {
  const initials = getInitials(name);
  const color = generateColorFromString(name);
  
  // Create SVG
  const svg = `
    <svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
      <circle cx="${size/2}" cy="${size/2}" r="${size/2}" fill="${color}"/>
      <text x="${size/2}" y="${size/2}" text-anchor="middle" dy="0.35em" 
            fill="white" font-family="Arial, sans-serif" 
            font-size="${size * 0.4}" font-weight="bold">${initials}</text>
    </svg>
  `;
  
  return `data:image/svg+xml;base64,${btoa(svg)}`;
};

/**
 * Generate a placeholder image data URI
 */
export const generatePlaceholderDataUri = (
  width: number = 300, 
  height: number = 200, 
  text?: string,
  backgroundColor: string = '#f0f0f0',
  textColor: string = '#666'
): string => {
  const displayText = text || `${width}×${height}`;
  const fontSize = Math.min(width, height) * 0.1;
  
  const svg = `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <rect width="${width}" height="${height}" fill="${backgroundColor}"/>
      <text x="${width/2}" y="${height/2}" text-anchor="middle" dy="0.35em" 
            fill="${textColor}" font-family="Arial, sans-serif" 
            font-size="${fontSize}">${displayText}</text>
    </svg>
  `;
  
  return `data:image/svg+xml;base64,${btoa(svg)}`;
};

/**
 * Get a safe image source with fallback
 */
export const getSafeImageSource = (
  imageUrl?: string | null, 
  fallbackName?: string,
  type: 'avatar' | 'banner' | 'thumbnail' = 'avatar'
) => {
  if (imageUrl && imageUrl.trim()) {
    return { uri: imageUrl };
  }
  
  // Generate appropriate fallback based on type
  switch (type) {
    case 'avatar':
      if (fallbackName) {
        return { uri: generateAvatarDataUri(fallbackName, 100) };
      }
      return { uri: generateAvatarDataUri('User', 100) };
      
    case 'banner':
      return { uri: generatePlaceholderDataUri(300, 150, 'No Image', '#e0e0e0', '#999') };
      
    case 'thumbnail':
      return { uri: generatePlaceholderDataUri(200, 150, 'No Image', '#e0e0e0', '#999') };
      
    default:
      return { uri: generatePlaceholderDataUri(150, 150, 'No Image', '#e0e0e0', '#999') };
  }
};

/**
 * React Native component props for safe image handling
 */
export const createImageProps = (
  imageUrl?: string | null,
  fallbackName?: string,
  type: 'avatar' | 'banner' | 'thumbnail' = 'avatar'
) => {
  return {
    source: getSafeImageSource(imageUrl, fallbackName, type),
    defaultSource: getSafeImageSource(undefined, fallbackName, type),
  };
};
