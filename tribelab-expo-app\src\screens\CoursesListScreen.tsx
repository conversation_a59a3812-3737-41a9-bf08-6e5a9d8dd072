import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  Alert,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation, useRoute } from '@react-navigation/native';
import { communityAPI, courseAPI } from '../services/api';

interface Course {
  _id: string;
  title: string;
  description: string;
  thumbnail?: string;
  instructor: {
    _id: string;
    name: string;
    profileImage?: string;
  };
  modules: any[];
  lessons: any[];
  enrolledStudents: string[];
  isPublished: boolean;
  price: number;
  currency: string;
  createdAt: string;
  updatedAt: string;
}

interface CourseProgress {
  courseId: string;
  completedLessons: string[];
  totalLessons: number;
  progressPercentage: number;
}

const CoursesListScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { slug, communityId } = route.params as { slug: string; communityId: string };

  const [courses, setCourses] = useState<Course[]>([]);
  const [courseProgress, setCourseProgress] = useState<{ [key: string]: CourseProgress }>({});
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);
  const [userId, setUserId] = useState<string>('');

  useEffect(() => {
    fetchCourses();
    fetchUserRole();
  }, [communityId]);

  const fetchUserRole = async () => {
    try {
      const storedUserId = await AsyncStorage.getItem('user_id');
      if (storedUserId) {
        setUserId(storedUserId);
      }

      // Fetch community details to check user role
      const response = await communityAPI.getCommunityBySlug(slug);
      const data = response.data;
      const isUserAdmin = data.admin === storedUserId;
      const isUserSubAdmin = data.subAdmins?.includes(storedUserId);
      setIsAdmin(isUserAdmin || isUserSubAdmin);
    } catch (error) {
      console.error('Error fetching user role:', error);
    }
  };

  const fetchCourses = async () => {
    try {
      const token = await AsyncStorage.getItem('auth_token');
      if (!token) {
        Alert.alert('Error', 'Please login again');
        return;
      }

      const response = await courseAPI.getCoursesByCommunity(communityId);
      const data = response.data;
      setCourses(data.courses || []);

      // Fetch progress for enrolled courses
      if (data.courses?.length > 0) {
        fetchCoursesProgress(data.courses);
      }
    } catch (error) {
      console.error('Error fetching courses:', error);
      Alert.alert('Error', 'Failed to load courses');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const fetchCoursesProgress = async (coursesList: Course[]) => {
    try {
      const progressPromises = coursesList.map(async (course) => {
        if (course.enrolledStudents.includes(userId)) {
          try {
            const response = await courseAPI.getProgressById(course._id);
            return {
              courseId: course._id,
              ...response.data,
            };
          } catch (error) {
            console.error(`Error fetching progress for course ${course._id}:`, error);
            return null;
          }
        }
        return null;
      });

      const progressResults = await Promise.all(progressPromises);
      const progressMap: { [key: string]: CourseProgress } = {};
      
      progressResults.forEach((progress) => {
        if (progress) {
          progressMap[progress.courseId] = progress;
        }
      });

      setCourseProgress(progressMap);
    } catch (error) {
      console.error('Error fetching course progress:', error);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    fetchCourses();
  };

  const enrollInCourse = async (courseId: string) => {
    try {
      await courseAPI.enrollInCourse(courseId);
      Alert.alert('Success', 'Successfully enrolled in course!');
      fetchCourses(); // Refresh to update enrollment status
    } catch (error: any) {
      console.error('Error enrolling in course:', error);
      Alert.alert('Error', error.response?.data?.error || 'Failed to enroll in course');
    }
  };

  const formatPrice = (price: number, currency: string = 'INR') => {
    if (price === 0) return 'Free';
    if (currency === 'INR') {
      return `₹${price}`;
    }
    return `${currency} ${price}`;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const renderCourse = ({ item }: { item: Course }) => {
    const isEnrolled = item.enrolledStudents.includes(userId);
    const progress = courseProgress[item._id];
    const totalLessons = item.lessons.length;

    return (
      <TouchableOpacity
        style={styles.courseCard}
        onPress={() => {
          if (isEnrolled || isAdmin) {
            navigation.navigate('CourseDetail', { 
              courseId: item._id, 
              slug, 
              communityId 
            });
          } else {
            // Show course preview or enrollment options
            Alert.alert(
              'Course Enrollment',
              `Would you like to enroll in "${item.title}"?`,
              [
                { text: 'Cancel', style: 'cancel' },
                { text: 'Enroll', onPress: () => enrollInCourse(item._id) },
              ]
            );
          }
        }}
      >
        {/* Course Thumbnail */}
        {item.thumbnail ? (
          <Image source={{ uri: item.thumbnail }} style={styles.courseThumbnail} />
        ) : (
          <View style={styles.defaultThumbnail}>
            <Ionicons name="school" size={40} color="#666" />
          </View>
        )}

        <View style={styles.courseContent}>
          {/* Course Header */}
          <View style={styles.courseHeader}>
            <Text style={styles.courseTitle} numberOfLines={2}>
              {item.title}
            </Text>
            <View style={styles.courseBadges}>
              {item.price === 0 ? (
                <View style={[styles.badge, styles.freeBadge]}>
                  <Text style={styles.badgeText}>Free</Text>
                </View>
              ) : (
                <View style={[styles.badge, styles.paidBadge]}>
                  <Text style={styles.badgeText}>{formatPrice(item.price, item.currency)}</Text>
                </View>
              )}
              {isEnrolled && (
                <View style={[styles.badge, styles.enrolledBadge]}>
                  <Ionicons name="checkmark-circle" size={12} color="#fff" />
                  <Text style={styles.badgeText}>Enrolled</Text>
                </View>
              )}
            </View>
          </View>

          {/* Course Description */}
          <Text style={styles.courseDescription} numberOfLines={3}>
            {item.description}
          </Text>

          {/* Course Stats */}
          <View style={styles.courseStats}>
            <View style={styles.statItem}>
              <Ionicons name="play-circle" size={16} color="#666" />
              <Text style={styles.statText}>{totalLessons} lessons</Text>
            </View>
            <View style={styles.statItem}>
              <Ionicons name="people" size={16} color="#666" />
              <Text style={styles.statText}>{item.enrolledStudents.length} students</Text>
            </View>
            <View style={styles.statItem}>
              <Ionicons name="time" size={16} color="#666" />
              <Text style={styles.statText}>Updated {formatDate(item.updatedAt)}</Text>
            </View>
          </View>

          {/* Progress Bar (for enrolled courses) */}
          {isEnrolled && progress && (
            <View style={styles.progressContainer}>
              <View style={styles.progressBar}>
                <View 
                  style={[
                    styles.progressFill, 
                    { width: `${progress.progressPercentage}%` }
                  ]} 
                />
              </View>
              <Text style={styles.progressText}>
                {progress.progressPercentage}% complete ({progress.completedLessons.length}/{progress.totalLessons})
              </Text>
            </View>
          )}

          {/* Instructor */}
          <View style={styles.instructorContainer}>
            {item.instructor.profileImage ? (
              <Image source={{ uri: item.instructor.profileImage }} style={styles.instructorAvatar} />
            ) : (
              <View style={styles.defaultInstructorAvatar}>
                <Ionicons name="person" size={16} color="#666" />
              </View>
            )}
            <Text style={styles.instructorName}>by {item.instructor.name}</Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Loading courses...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#007AFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Courses</Text>
        {isAdmin && (
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => navigation.navigate('CourseCreate', { slug, communityId })}
          >
            <Ionicons name="add" size={24} color="#007AFF" />
          </TouchableOpacity>
        )}
      </View>

      <FlatList
        data={courses}
        renderItem={renderCourse}
        keyExtractor={(item) => item._id}
        style={styles.coursesList}
        contentContainerStyle={styles.coursesListContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="school" size={48} color="#ccc" />
            <Text style={styles.emptyText}>No courses available</Text>
            {isAdmin && (
              <TouchableOpacity
                style={styles.createCourseButton}
                onPress={() => navigation.navigate('CourseCreate', { slug, communityId })}
              >
                <Text style={styles.createCourseButtonText}>Create First Course</Text>
              </TouchableOpacity>
            )}
          </View>
        }
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e1e5e9',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1a1a1a',
  },
  addButton: {
    padding: 8,
  },
  coursesList: {
    flex: 1,
  },
  coursesListContent: {
    padding: 16,
  },
  courseCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden',
  },
  courseThumbnail: {
    width: '100%',
    height: 180,
    resizeMode: 'cover',
  },
  defaultThumbnail: {
    width: '100%',
    height: 180,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  courseContent: {
    padding: 16,
  },
  courseHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  courseTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1a1a1a',
    flex: 1,
    marginRight: 8,
  },
  courseBadges: {
    flexDirection: 'row',
    gap: 4,
  },
  badge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  freeBadge: {
    backgroundColor: '#28a745',
  },
  paidBadge: {
    backgroundColor: '#007AFF',
  },
  enrolledBadge: {
    backgroundColor: '#ffc107',
  },
  badgeText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '600',
  },
  courseDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 12,
  },
  courseStats: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    marginBottom: 12,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  statText: {
    fontSize: 12,
    color: '#666',
  },
  progressContainer: {
    marginBottom: 12,
  },
  progressBar: {
    height: 4,
    backgroundColor: '#e1e5e9',
    borderRadius: 2,
    marginBottom: 4,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#007AFF',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 12,
    color: '#666',
  },
  instructorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  instructorAvatar: {
    width: 24,
    height: 24,
    borderRadius: 12,
    marginRight: 8,
  },
  defaultInstructorAvatar: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  instructorName: {
    fontSize: 12,
    color: '#666',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    marginTop: 16,
    marginBottom: 20,
  },
  createCourseButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  createCourseButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default CoursesListScreen;
