import { useNavigation } from '@react-navigation/native';
import { useCallback } from 'react';

export const useSafeNavigation = () => {
  const navigation = useNavigation();

  const safeNavigate = useCallback((routeName: string, params?: any) => {
    try {
      if (navigation && navigation.navigate) {
        navigation.navigate(routeName as never, params as never);
      } else {
        console.warn('Navigation not available, skipping navigation to:', routeName);
      }
    } catch (error) {
      console.error('Navigation error:', error);
      console.warn('Failed to navigate to:', routeName);
    }
  }, [navigation]);

  const safeGoBack = useCallback(() => {
    try {
      if (navigation && navigation.goBack && navigation.canGoBack()) {
        navigation.goBack();
      } else {
        console.warn('Cannot go back, navigation not available or no previous screen');
      }
    } catch (error) {
      console.error('Go back error:', error);
    }
  }, [navigation]);

  const safeReset = useCallback((state: any) => {
    try {
      if (navigation && navigation.reset) {
        navigation.reset(state);
      } else {
        console.warn('Reset navigation not available');
      }
    } catch (error) {
      console.error('Reset navigation error:', error);
    }
  }, [navigation]);

  return {
    navigate: safeNavigate,
    goBack: safeGoBack,
    reset: safeReset,
    navigation,
  };
};
