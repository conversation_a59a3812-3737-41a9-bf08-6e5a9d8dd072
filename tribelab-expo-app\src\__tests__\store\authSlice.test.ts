import { configureStore } from '@reduxjs/toolkit';
import authReducer, {
  loginUser,
  registerUser,
  logoutUser,
  clearError,
  setUser,
  AuthState,
} from '../../store/slices/authSlice';

// Mock the auth API
jest.mock('../../services/api', () => ({
  authAPI: {
    login: jest.fn(),
    register: jest.fn(),
    logout: jest.fn(),
    refreshToken: jest.fn(),
    getProfile: jest.fn(),
    googleLogin: jest.fn(),
    verifyEmail: jest.fn(),
    resendVerification: jest.fn(),
  },
}));

describe('Auth Slice', () => {
  let store: any;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        auth: authReducer,
      },
    });
  });

  describe('Initial State', () => {
    it('should have correct initial state', () => {
      const state = store.getState().auth;
      expect(state).toEqual({
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      });
    });
  });

  describe('Synchronous Actions', () => {
    it('should handle setUser', () => {
      const user = {
        id: '1',
        username: 'testuser',
        email: '<EMAIL>',
        name: 'Test User',
      };

      store.dispatch(setUser(user));
      const state = store.getState().auth;

      expect(state.user).toEqual(user);
      expect(state.isAuthenticated).toBe(true);
    });

    it('should handle clearError', () => {
      // First set an error
      store.dispatch({ type: 'auth/loginUser/rejected', payload: 'Test error' });
      expect(store.getState().auth.error).toBe('Test error');

      // Then clear it
      store.dispatch(clearError());
      expect(store.getState().auth.error).toBeNull();
    });

    it('should handle logoutUser', () => {
      // First set a user
      const user = { id: '1', username: 'testuser', email: '<EMAIL>' };
      store.dispatch(setUser(user));

      // Then logout
      store.dispatch(logoutUser());
      const state = store.getState().auth;

      expect(state.user).toBeNull();
      expect(state.token).toBeNull();
      expect(state.isAuthenticated).toBe(false);
      expect(state.error).toBeNull();
    });
  });

  describe('Async Actions', () => {
    it('should handle loginUser pending', () => {
      store.dispatch({ type: 'auth/loginUser/pending' });
      const state = store.getState().auth;

      expect(state.isLoading).toBe(true);
      expect(state.error).toBeNull();
    });

    it('should handle loginUser fulfilled', () => {
      const payload = {
        user: { id: '1', username: 'testuser', email: '<EMAIL>' },
        token: 'test-token',
      };

      store.dispatch({ type: 'auth/loginUser/fulfilled', payload });
      const state = store.getState().auth;

      expect(state.isLoading).toBe(false);
      expect(state.isAuthenticated).toBe(true);
      expect(state.user).toEqual(payload.user);
      expect(state.token).toBe(payload.token);
      expect(state.error).toBeNull();
    });

    it('should handle loginUser rejected', () => {
      const errorMessage = 'Invalid credentials';

      store.dispatch({ type: 'auth/loginUser/rejected', payload: errorMessage });
      const state = store.getState().auth;

      expect(state.isLoading).toBe(false);
      expect(state.isAuthenticated).toBe(false);
      expect(state.user).toBeNull();
      expect(state.token).toBeNull();
      expect(state.error).toBe(errorMessage);
    });

    it('should handle registerUser pending', () => {
      store.dispatch({ type: 'auth/registerUser/pending' });
      const state = store.getState().auth;

      expect(state.isLoading).toBe(true);
      expect(state.error).toBeNull();
    });

    it('should handle registerUser fulfilled', () => {
      const payload = {
        user: { id: '1', username: 'newuser', email: '<EMAIL>' },
        token: 'new-token',
      };

      store.dispatch({ type: 'auth/registerUser/fulfilled', payload });
      const state = store.getState().auth;

      expect(state.isLoading).toBe(false);
      expect(state.isAuthenticated).toBe(true);
      expect(state.user).toEqual(payload.user);
      expect(state.token).toBe(payload.token);
      expect(state.error).toBeNull();
    });

    it('should handle registerUser rejected', () => {
      const errorMessage = 'User already exists';

      store.dispatch({ type: 'auth/registerUser/rejected', payload: errorMessage });
      const state = store.getState().auth;

      expect(state.isLoading).toBe(false);
      expect(state.isAuthenticated).toBe(false);
      expect(state.user).toBeNull();
      expect(state.token).toBeNull();
      expect(state.error).toBe(errorMessage);
    });
  });

  describe('Edge Cases', () => {
    it('should maintain state consistency during multiple actions', () => {
      // Start with login pending
      store.dispatch({ type: 'auth/loginUser/pending' });
      expect(store.getState().auth.isLoading).toBe(true);

      // Then login success
      const user = { id: '1', username: 'testuser', email: '<EMAIL>' };
      store.dispatch({
        type: 'auth/loginUser/fulfilled',
        payload: { user, token: 'token' },
      });

      const state = store.getState().auth;
      expect(state.isLoading).toBe(false);
      expect(state.isAuthenticated).toBe(true);
      expect(state.user).toEqual(user);
    });

    it('should clear previous errors on new login attempt', () => {
      // Set an error
      store.dispatch({ type: 'auth/loginUser/rejected', payload: 'Old error' });
      expect(store.getState().auth.error).toBe('Old error');

      // Start new login
      store.dispatch({ type: 'auth/loginUser/pending' });
      expect(store.getState().auth.error).toBeNull();
    });
  });
});
