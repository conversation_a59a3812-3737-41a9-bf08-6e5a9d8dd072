/**
 * @flow strict
 */

import type { Icon } from './index';

export type OcticonsGlyphs = 'accessibility' | 'alert' | 'apps' | 'archive' | 'arrow-both' | 'arrow-down' | 'arrow-left' | 'arrow-right' | 'arrow-switch' | 'arrow-up' | 'beaker' | 'bell' | 'bell-fill' | 'bell-slash' | 'blocked' | 'bold' | 'book' | 'bookmark' | 'bookmark-slash' | 'briefcase' | 'broadcast' | 'browser' | 'bug' | 'calendar' | 'check' | 'check-circle' | 'check-circle-fill' | 'checklist' | 'chevron-down' | 'chevron-left' | 'chevron-right' | 'chevron-up' | 'circle' | 'circle-slash' | 'clock' | 'code' | 'code-of-conduct' | 'code-review' | 'code-square' | 'codescan' | 'codescan-checkmark' | 'codespaces' | 'columns' | 'comment' | 'comment-discussion' | 'container' | 'copy' | 'cpu' | 'credit-card' | 'cross-reference' | 'dash' | 'database' | 'dependabot' | 'desktop-download' | 'device-camera' | 'device-camera-video' | 'device-desktop' | 'device-mobile' | 'diamond' | 'diff' | 'diff-added' | 'diff-ignored' | 'diff-modified' | 'diff-removed' | 'diff-renamed' | 'dot' | 'dot-fill' | 'download' | 'duplicate' | 'ellipsis' | 'eye' | 'eye-closed' | 'feed-discussion' | 'feed-heart' | 'feed-person' | 'feed-repo' | 'feed-rocket' | 'feed-star' | 'feed-tag' | 'file' | 'file-badge' | 'file-binary' | 'file-code' | 'file-diff' | 'file-directory' | 'file-submodule' | 'file-symlink-file' | 'file-zip' | 'filter' | 'flame' | 'fold' | 'fold-down' | 'fold-up' | 'gear' | 'gift' | 'git-branch' | 'git-commit' | 'git-compare' | 'git-merge' | 'git-pull-request' | 'git-pull-request-closed' | 'git-pull-request-draft' | 'globe' | 'grabber' | 'graph' | 'hash' | 'heading' | 'heart' | 'heart-fill' | 'history' | 'home' | 'horizontal-rule' | 'hourglass' | 'hubot' | 'id-badge' | 'image' | 'inbox' | 'infinity' | 'info' | 'issue-closed' | 'issue-draft' | 'issue-opened' | 'issue-reopened' | 'italic' | 'iterations' | 'kebab-horizontal' | 'key' | 'key-asterisk' | 'law' | 'light-bulb' | 'link' | 'link-external' | 'list-ordered' | 'list-unordered' | 'location' | 'lock' | 'log' | 'logo-gist' | 'logo-github' | 'mail' | 'mark-github' | 'markdown' | 'megaphone' | 'mention' | 'meter' | 'milestone' | 'mirror' | 'moon' | 'mortar-board' | 'multi-select' | 'mute' | 'no-entry' | 'north-star' | 'note' | 'number' | 'organization' | 'package' | 'package-dependencies' | 'package-dependents' | 'paintbrush' | 'paper-airplane' | 'paste' | 'pencil' | 'people' | 'person' | 'person-add' | 'person-fill' | 'pin' | 'play' | 'plug' | 'plus' | 'plus-circle' | 'project' | 'pulse' | 'question' | 'quote' | 'reply' | 'repo' | 'repo-clone' | 'repo-deleted' | 'repo-forked' | 'repo-pull' | 'repo-push' | 'repo-template' | 'report' | 'rocket' | 'rows' | 'rss' | 'ruby' | 'screen-full' | 'screen-normal' | 'search' | 'server' | 'share' | 'share-android' | 'shield' | 'shield-check' | 'shield-lock' | 'shield-x' | 'sidebar-collapse' | 'sidebar-expand' | 'sign-in' | 'sign-out' | 'single-select' | 'skip' | 'smiley' | 'sort-asc' | 'sort-desc' | 'square' | 'square-fill' | 'squirrel' | 'stack' | 'star' | 'star-fill' | 'stop' | 'stopwatch' | 'strikethrough' | 'sun' | 'sync' | 'tab-external' | 'table' | 'tag' | 'tasklist' | 'telescope' | 'telescope-fill' | 'terminal' | 'three-bars' | 'thumbsdown' | 'thumbsup' | 'tools' | 'trash' | 'triangle-down' | 'triangle-left' | 'triangle-right' | 'triangle-up' | 'typography' | 'unfold' | 'unlock' | 'unmute' | 'unverified' | 'upload' | 'verified' | 'versions' | 'video' | 'webhook' | 'workflow' | 'x' | 'x-circle' | 'x-circle-fill' | 'zap';

declare export default Class<Icon<OcticonsGlyphs>>;
