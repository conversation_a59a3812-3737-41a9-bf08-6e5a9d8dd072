import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Image,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/Ionicons';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { createChat, setCurrentChat } from '../store/slices/chatSlice';
import { searchUsers } from '../store/slices/userSlice';
import Toast from 'react-native-toast-message';

interface User {
  id: string;
  name: string;
  username?: string;
  avatar?: string;
  isOnline: boolean;
  lastSeen?: string;
}

const NewChatScreen = () => {
  const navigation = useNavigation();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector(state => state.auth);
  
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<User[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<User[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [chatType, setChatType] = useState<'direct' | 'group'>('direct');
  const [groupName, setGroupName] = useState('');

  // Load users from API
  const [allUsers, setAllUsers] = useState<User[]>([]);

  useEffect(() => {
    if (searchQuery.trim()) {
      handleSearch();
    } else {
      setSearchResults(allUsers);
    }
  }, [searchQuery, allUsers]);

  useEffect(() => {
    // Load initial users from API
    loadUsers();
  }, []);

  const loadUsers = async () => {
    try {
      setIsSearching(true);
      const results = await dispatch(searchUsers('')).unwrap();
      setAllUsers(results);
      setSearchResults(results);
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Load Failed',
        text2: 'Failed to load users',
      });
    } finally {
      setIsSearching(false);
    }
  };

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      setSearchResults(allUsers);
      return;
    }

    try {
      setIsSearching(true);

      // Use real API for search
      const results = await dispatch(searchUsers(searchQuery)).unwrap();
      setSearchResults(results);
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Search Failed',
        text2: 'Failed to search users',
      });
    } finally {
      setIsSearching(false);
    }
  };

  const handleUserSelect = (selectedUser: User) => {
    if (chatType === 'direct') {
      // For direct chat, immediately create chat with this user
      createDirectChat(selectedUser);
    } else {
      // For group chat, add to selected users
      if (selectedUsers.find(u => u.id === selectedUser.id)) {
        setSelectedUsers(prev => prev.filter(u => u.id !== selectedUser.id));
      } else {
        setSelectedUsers(prev => [...prev, selectedUser]);
      }
    }
  };

  const createDirectChat = async (otherUser: User) => {
    try {
      setIsCreating(true);
      
      const newChat = await dispatch(createChat({
        participantIds: [user!.id, otherUser.id],
        type: 'direct',
      })).unwrap();

      dispatch(setCurrentChat(newChat));
      navigation.replace('ChatDetail', { chatId: newChat.id });
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Chat Creation Failed',
        text2: error.message || 'Failed to create chat',
      });
    } finally {
      setIsCreating(false);
    }
  };

  const createGroupChat = async () => {
    if (selectedUsers.length < 2) {
      Alert.alert('Error', 'Please select at least 2 users for a group chat');
      return;
    }

    if (!groupName.trim()) {
      Alert.alert('Error', 'Please enter a group name');
      return;
    }

    try {
      setIsCreating(true);
      
      const participantIds = [user!.id, ...selectedUsers.map(u => u.id)];
      
      const newChat = await dispatch(createChat({
        participantIds,
        type: 'group',
        name: groupName.trim(),
      })).unwrap();

      dispatch(setCurrentChat(newChat));
      navigation.replace('ChatDetail', { chatId: newChat.id });
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Group Creation Failed',
        text2: error.message || 'Failed to create group chat',
      });
    } finally {
      setIsCreating(false);
    }
  };

  const formatLastSeen = (lastSeen?: string) => {
    if (!lastSeen) return '';
    
    const date = new Date(lastSeen);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else {
      return `${Math.floor(diffInHours / 24)}d ago`;
    }
  };

  const renderUser = ({ item: user }: { item: User }) => {
    const isSelected = selectedUsers.find(u => u.id === user.id);
    
    return (
      <TouchableOpacity
        style={[styles.userItem, isSelected && styles.selectedUserItem]}
        onPress={() => handleUserSelect(user)}
        disabled={isCreating}
      >
        <View style={styles.userInfo}>
          <View style={styles.avatarContainer}>
            {user.avatar ? (
              <Image
                source={{ uri: user.avatar }}
                style={styles.userAvatar}
              />
            ) : (
              <View style={[styles.userAvatar, styles.defaultAvatar]}>
                <Text style={styles.defaultAvatarText}>
                  {user.name.charAt(0).toUpperCase()}
                </Text>
              </View>
            )}
            {user.isOnline && <View style={styles.onlineIndicator} />}
          </View>
          
          <View style={styles.userDetails}>
            <Text style={styles.userName}>{user.name}</Text>
            {user.username && (
              <Text style={styles.userUsername}>@{user.username}</Text>
            )}
            <Text style={styles.userStatus}>
              {user.isOnline ? 'Online' : `Last seen ${formatLastSeen(user.lastSeen)}`}
            </Text>
          </View>
        </View>
        
        {chatType === 'group' && (
          <View style={[styles.checkbox, isSelected && styles.checkboxSelected]}>
            {isSelected && <Icon name="checkmark" size={16} color="#fff" />}
          </View>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>New Chat</Text>
        <View style={styles.placeholder} />
      </View>

      {/* Chat Type Selector */}
      <View style={styles.chatTypeSelector}>
        <TouchableOpacity
          style={[styles.chatTypeButton, chatType === 'direct' && styles.chatTypeButtonActive]}
          onPress={() => setChatType('direct')}
        >
          <Text style={[styles.chatTypeText, chatType === 'direct' && styles.chatTypeTextActive]}>
            Direct
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.chatTypeButton, chatType === 'group' && styles.chatTypeButtonActive]}
          onPress={() => setChatType('group')}
        >
          <Text style={[styles.chatTypeText, chatType === 'group' && styles.chatTypeTextActive]}>
            Group
          </Text>
        </TouchableOpacity>
      </View>

      {/* Group Name Input */}
      {chatType === 'group' && (
        <View style={styles.groupNameContainer}>
          <TextInput
            style={styles.groupNameInput}
            value={groupName}
            onChangeText={setGroupName}
            placeholder="Enter group name"
            placeholderTextColor="#999"
          />
        </View>
      )}

      {/* Search */}
      <View style={styles.searchContainer}>
        <Icon name="search" size={20} color="#999" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholder="Search users"
          placeholderTextColor="#999"
        />
        {isSearching && <ActivityIndicator size="small" color="#007AFF" />}
      </View>

      {/* Selected Users (for group chat) */}
      {chatType === 'group' && selectedUsers.length > 0 && (
        <View style={styles.selectedUsersContainer}>
          <Text style={styles.selectedUsersTitle}>
            Selected ({selectedUsers.length})
          </Text>
          <FlatList
            horizontal
            data={selectedUsers}
            renderItem={({ item }) => (
              <View style={styles.selectedUserChip}>
                {item.avatar ? (
                  <Image
                    source={{ uri: item.avatar }}
                    style={styles.selectedUserAvatar}
                  />
                ) : (
                  <View style={[styles.selectedUserAvatar, styles.defaultAvatar]}>
                    <Text style={styles.defaultAvatarText}>
                      {item.name.charAt(0).toUpperCase()}
                    </Text>
                  </View>
                )}
                <Text style={styles.selectedUserName}>{item.name}</Text>
                <TouchableOpacity
                  onPress={() => handleUserSelect(item)}
                  style={styles.removeUserButton}
                >
                  <Icon name="close" size={16} color="#666" />
                </TouchableOpacity>
              </View>
            )}
            keyExtractor={(item) => item.id}
            showsHorizontalScrollIndicator={false}
            style={styles.selectedUsersList}
          />
        </View>
      )}

      {/* Users List */}
      <FlatList
        data={searchResults}
        renderItem={renderUser}
        keyExtractor={(item) => item.id}
        style={styles.usersList}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Icon name="people-outline" size={64} color="#ccc" />
            <Text style={styles.emptyTitle}>No Users Found</Text>
            <Text style={styles.emptyText}>
              Try searching with a different term
            </Text>
          </View>
        }
      />

      {/* Create Group Button */}
      {chatType === 'group' && selectedUsers.length >= 2 && (
        <View style={styles.createButtonContainer}>
          <TouchableOpacity
            style={[styles.createButton, isCreating && styles.createButtonDisabled]}
            onPress={createGroupChat}
            disabled={isCreating}
          >
            {isCreating ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <Text style={styles.createButtonText}>
                Create Group ({selectedUsers.length + 1})
              </Text>
            )}
          </TouchableOpacity>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  placeholder: {
    width: 34,
  },
  chatTypeSelector: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    marginHorizontal: 20,
    marginTop: 16,
    borderRadius: 8,
    padding: 4,
  },
  chatTypeButton: {
    flex: 1,
    paddingVertical: 10,
    alignItems: 'center',
    borderRadius: 6,
  },
  chatTypeButtonActive: {
    backgroundColor: '#007AFF',
  },
  chatTypeText: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
  },
  chatTypeTextActive: {
    color: '#fff',
    fontWeight: '600',
  },
  groupNameContainer: {
    paddingHorizontal: 20,
    paddingTop: 16,
  },
  groupNameInput: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    marginHorizontal: 20,
    marginTop: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  selectedUsersContainer: {
    backgroundColor: '#fff',
    marginHorizontal: 20,
    marginTop: 16,
    borderRadius: 8,
    padding: 16,
  },
  selectedUsersTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  selectedUsersList: {
    maxHeight: 60,
  },
  selectedUserChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    borderRadius: 20,
    paddingLeft: 4,
    paddingRight: 8,
    paddingVertical: 4,
    marginRight: 8,
  },
  selectedUserAvatar: {
    width: 24,
    height: 24,
    borderRadius: 12,
    marginRight: 6,
  },
  selectedUserName: {
    fontSize: 14,
    color: '#333',
    marginRight: 4,
  },
  removeUserButton: {
    padding: 2,
  },
  usersList: {
    flex: 1,
    marginTop: 16,
  },
  userItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#fff',
    marginHorizontal: 20,
    marginBottom: 8,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#eee',
  },
  selectedUserItem: {
    borderColor: '#007AFF',
    backgroundColor: '#f0f8ff',
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 12,
  },
  userAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  defaultAvatar: {
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  defaultAvatarText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 14,
    height: 14,
    borderRadius: 7,
    backgroundColor: '#34C759',
    borderWidth: 2,
    borderColor: '#fff',
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  userUsername: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  userStatus: {
    fontSize: 12,
    color: '#999',
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#ddd',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxSelected: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  createButtonContainer: {
    padding: 20,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  createButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: 'center',
  },
  createButtonDisabled: {
    backgroundColor: '#ccc',
  },
  createButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default NewChatScreen;
