{"expo": {"name": "tribelab-expo-app", "slug": "tribelab-expo-app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "tribelab-expo-app", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": [["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], "expo-video", ["@react-native-google-signin/google-signin", {"iosUrlScheme": "com.googleusercontent.apps.1007412145112-hs12376c7290psdmt5cu042p2hpr6phk"}]]}}