import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { authAPI } from '../../services/api';
import { googleAuthService, GoogleUser } from '../../services/googleAuth';

export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  role: 'user' | 'admin' | 'moderator';
  isEmailVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  isEmailVerificationRequired: boolean;
}

const initialState: AuthState = {
  user: null,
  token: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  isEmailVerificationRequired: false,
};

// Async thunks
export const loginUser = createAsyncThunk(
  'auth/login',
  async ({ email, password }: { email: string; password: string }, { rejectWithValue }) => {
    try {
      const response = await authAPI.login(email, password);
      const { user, token } = response.data;
      
      // Store token and last activity in AsyncStorage
      await AsyncStorage.setItem('auth_token', token);
      await AsyncStorage.setItem('last_activity', new Date().toISOString());

      return { user, token };
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Login failed');
    }
  }
);

export const registerUser = createAsyncThunk(
  'auth/register',
  async (userData: { username: string; email: string; password: string }, { rejectWithValue }) => {
    try {
      const response = await authAPI.register(userData);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Registration failed');
    }
  }
);

export const verifyEmail = createAsyncThunk(
  'auth/verifyEmail',
  async (token: string, { rejectWithValue }) => {
    try {
      const response = await authAPI.verifyEmail(token);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Email verification failed');
    }
  }
);

export const resendVerification = createAsyncThunk(
  'auth/resendVerification',
  async (email: string, { rejectWithValue }) => {
    try {
      const response = await authAPI.resendVerification(email);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to resend verification');
    }
  }
);

export const logoutUser = createAsyncThunk(
  'auth/logout',
  async (_, { rejectWithValue }) => {
    try {
      // Clear token and activity from AsyncStorage
      await AsyncStorage.removeItem('auth_token');
      await AsyncStorage.removeItem('refresh_token');
      await AsyncStorage.removeItem('last_activity');
      return true;
    } catch (error: any) {
      return rejectWithValue('Logout failed');
    }
  }
);

// Check authentication status and restore session
export const checkAuthStatus = createAsyncThunk(
  'auth/checkStatus',
  async (_, { rejectWithValue }) => {
    try {
      const token = await AsyncStorage.getItem('auth_token');
      const lastActivity = await AsyncStorage.getItem('last_activity');

      if (!token) {
        return { isAuthenticated: false };
      }

      // Check for 1-month inactivity timeout (30 days)
      if (lastActivity) {
        const lastActivityDate = new Date(lastActivity);
        const now = new Date();
        const daysSinceLastActivity = (now.getTime() - lastActivityDate.getTime()) / (1000 * 60 * 60 * 24);

        if (daysSinceLastActivity > 30) {
          // Session expired due to inactivity, clear all tokens
          await AsyncStorage.removeItem('auth_token');
          await AsyncStorage.removeItem('refresh_token');
          await AsyncStorage.removeItem('last_activity');
          return { isAuthenticated: false, sessionExpired: true };
        }
      }

      // Update last activity timestamp
      await AsyncStorage.setItem('last_activity', new Date().toISOString());

      // Check if token is expired (basic check)
      const tokenData = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;

      if (tokenData.exp < currentTime) {
        // Token expired, try to refresh
        const refreshToken = await AsyncStorage.getItem('refresh_token');
        if (refreshToken) {
          try {
            const response = await authAPI.refreshToken(refreshToken);
            const { user, token: newToken } = response.data;
            await AsyncStorage.setItem('auth_token', newToken);
            return { user, token: newToken, isAuthenticated: true };
          } catch (refreshError) {
            // Refresh failed, clear tokens
            await AsyncStorage.removeItem('auth_token');
            await AsyncStorage.removeItem('refresh_token');
            return { isAuthenticated: false };
          }
        } else {
          // No refresh token, clear auth token
          await AsyncStorage.removeItem('auth_token');
          return { isAuthenticated: false };
        }
      }

      // Token is valid, get user profile
      const response = await authAPI.getProfile();
      return { user: response.data.user, token, isAuthenticated: true };
    } catch (error: any) {
      // Clear invalid tokens
      await AsyncStorage.removeItem('auth_token');
      await AsyncStorage.removeItem('refresh_token');
      return { isAuthenticated: false };
    }
  }
);

// Google OAuth login
export const loginWithGoogle = createAsyncThunk(
  'auth/loginWithGoogle',
  async (_, { rejectWithValue }) => {
    try {
      // Get Google user info
      const googleUser = await googleAuthService.signIn();

      if (!googleUser) {
        return rejectWithValue('Google sign-in was cancelled');
      }

      // Send Google user info to backend for authentication
      const response = await authAPI.googleLogin({
        googleId: googleUser.id,
        email: googleUser.email,
        name: googleUser.name,
        picture: googleUser.picture,
      });

      const { user, token } = response.data;

      // Store token and last activity in AsyncStorage
      await AsyncStorage.setItem('auth_token', token);
      await AsyncStorage.setItem('last_activity', new Date().toISOString());

      return { user, token };
    } catch (error: any) {
      console.error('Google login error:', error);

      // Handle specific Google Auth errors
      if (error.message?.includes('Google OAuth is not properly configured')) {
        return rejectWithValue('Google sign-in is not available. Please use email and password to login.');
      }

      if (error.message?.includes('network') || error.message?.includes('fetch')) {
        return rejectWithValue('Network error. Please check your internet connection and try again.');
      }
      return rejectWithValue(error.response?.data?.message || error.message || 'Google login failed');
    }
  }
);

// Auth slice
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setEmailVerificationRequired: (state, action: PayloadAction<boolean>) => {
      state.isEmailVerificationRequired = action.payload;
    },
    updateUser: (state, action: PayloadAction<Partial<User>>) => {
      if (state.user) {
        state.user = { ...state.user, ...action.payload };
      }
    },
  },
  extraReducers: (builder) => {
    // Login
    builder
      .addCase(loginUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.isAuthenticated = true;
        state.isEmailVerificationRequired = !action.payload.user.isEmailVerified;
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Register
      .addCase(registerUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(registerUser.fulfilled, (state) => {
        state.isLoading = false;
        state.isEmailVerificationRequired = true;
      })
      .addCase(registerUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Email verification
      .addCase(verifyEmail.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(verifyEmail.fulfilled, (state) => {
        state.isLoading = false;
        state.isEmailVerificationRequired = false;
        if (state.user) {
          state.user.isEmailVerified = true;
        }
      })
      .addCase(verifyEmail.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Google Login
      .addCase(loginWithGoogle.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loginWithGoogle.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.isAuthenticated = true;
        state.isEmailVerificationRequired = !action.payload.user.isEmailVerified;
      })
      .addCase(loginWithGoogle.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Logout
      .addCase(logoutUser.fulfilled, (state) => {
        state.user = null;
        state.token = null;
        state.isAuthenticated = false;
        state.isEmailVerificationRequired = false;
        state.error = null;
      })

      // Check auth status
      .addCase(checkAuthStatus.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(checkAuthStatus.fulfilled, (state, action) => {
        state.isLoading = false;
        if (action.payload.isAuthenticated) {
          state.user = action.payload.user;
          state.token = action.payload.token;
          state.isAuthenticated = true;
          state.isEmailVerificationRequired = !action.payload.user?.isEmailVerified;
        } else {
          state.user = null;
          state.token = null;
          state.isAuthenticated = false;
          state.isEmailVerificationRequired = false;
        }
        state.error = null;
      })
      .addCase(checkAuthStatus.rejected, (state) => {
        state.isLoading = false;
        state.user = null;
        state.token = null;
        state.isAuthenticated = false;
        state.isEmailVerificationRequired = false;
        state.error = null;
      });
  },
});

export const { clearError, setEmailVerificationRequired, updateUser } = authSlice.actions;
export default authSlice.reducer;
