import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "@/lib/auth-helpers";
import { dbconnect } from "@/lib/db";
import { User } from "@/models/User";
import { Community } from "@/models/Community";

export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ communityId: string }> }
) {
  try {
    const session = await getServerSession();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    await dbconnect();

    // Check if user is admin
    const adminUser = await User.findById(session.user.id);
    if (!adminUser || !adminUser.isAdmin) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }

    const params = await context.params;
    const { communityId } = params;
    const { status, reason } = await request.json();

    // Validate input
    if (!status || !reason) {
      return NextResponse.json(
        { error: "Status and reason are required" },
        { status: 400 }
      );
    }

    const validStatuses = ["active", "suspended", "under_review"];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: "Invalid status. Must be one of: active, suspended, under_review" },
        { status: 400 }
      );
    }

    // Find the community to update
    const community = await Community.findById(communityId).populate("admin", "username email");
    if (!community) {
      return NextResponse.json({ error: "Community not found" }, { status: 404 });
    }

    const previousStatus = community.status;

    // Update community status
    community.status = status;
    
    // Add to admin actions log
    const adminAction = {
      adminId: session.user.id,
      targetCommunityId: communityId,
      action: `status_change_${status}`,
      reason,
      timestamp: new Date(),
      previousStatus
    };

    // Save the admin action to community's history
    if (!community.adminActions) {
      community.adminActions = [];
    }
    community.adminActions.push(adminAction);

    await community.save();

    // Log the action for audit purposes
    console.log(`Admin ${adminUser.username} changed community ${community.name} status to ${status}. Reason: ${reason}`);

    // If community is suspended, you might want to notify the community owner
    if (status === "suspended") {
      // Here you could send an email notification to the community owner
      // or create a notification in your notification system
    }

    return NextResponse.json({
      success: true,
      message: `Community status updated to ${status}`,
      community: {
        id: community._id,
        name: community.name,
        slug: community.slug,
        status: community.status,
        updatedAt: new Date(),
        owner: {
          id: community.admin?._id,
          username: community.admin?.username,
          email: community.admin?.email
        }
      },
      adminAction
    });

  } catch (error) {
    console.error("Error updating community status:", error);
    return NextResponse.json(
      { error: "Failed to update community status" },
      { status: 500 }
    );
  }
}
