#!/usr/bin/env node

/**
 * Authentication Flow Test Script
 * 
 * This script tests the complete authentication flow including:
 * - User registration with validation
 * - User login with security checks
 * - Token generation and validation
 * - Session management
 * - Error handling
 */

import dotenv from 'dotenv';
import { connectDB } from '../config/database.js';
import { User } from '../models/User.js';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';

// Load environment variables
dotenv.config();

// Test data
const testUsers = [
  {
    username: 'authtest1',
    email: '<EMAIL>',
    password: 'AuthTest123!',
    name: 'Auth Test User 1'
  },
  {
    username: 'authtest2',
    email: '<EMAIL>',
    password: 'AuthTest456!',
    name: 'Auth Test User 2'
  }
];

async function testAuthenticationFlow() {
  console.log('🔐 Testing Authentication Flow...\n');

  try {
    // Connect to database
    await connectDB();
    console.log('✅ Database connected\n');

    // Clean up any existing test users
    await User.deleteMany({ 
      email: { $in: testUsers.map(u => u.email) } 
    });

    let testResults = {
      registration: { passed: 0, failed: 0 },
      login: { passed: 0, failed: 0 },
      tokenValidation: { passed: 0, failed: 0 },
      security: { passed: 0, failed: 0 }
    };

    // Test 1: User Registration
    console.log('1️⃣ Testing User Registration...');
    for (const userData of testUsers) {
      try {
        // Validate input data
        if (!userData.username || userData.username.length < 3) {
          throw new Error('Username validation failed');
        }
        
        if (!userData.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(userData.email)) {
          throw new Error('Email validation failed');
        }
        
        if (!userData.password || userData.password.length < 8) {
          throw new Error('Password validation failed');
        }

        // Check password complexity
        const hasUpperCase = /[A-Z]/.test(userData.password);
        const hasLowerCase = /[a-z]/.test(userData.password);
        const hasNumbers = /\d/.test(userData.password);
        const hasSpecialChar = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(userData.password);

        if (!hasUpperCase || !hasLowerCase || !hasNumbers || !hasSpecialChar) {
          throw new Error('Password complexity validation failed');
        }

        // Create user (let the model handle password hashing)
        const user = new User({
          username: userData.username,
          email: userData.email.toLowerCase(),
          password: userData.password, // Raw password - model will hash it
          name: userData.name,
          isEmailVerified: true // Skip email verification for testing
        });

        await user.save();
        console.log(`   ✅ User registered: ${userData.username}`);
        testResults.registration.passed++;

      } catch (error) {
        console.log(`   ❌ Registration failed for ${userData.username}: ${error.message}`);
        testResults.registration.failed++;
      }
    }

    // Test 2: User Login
    console.log('\n2️⃣ Testing User Login...');
    for (const userData of testUsers) {
      try {
        // Find user
        const user = await User.findOne({ email: userData.email.toLowerCase() });
        if (!user) {
          throw new Error('User not found');
        }

        // Verify password using the model's method
        const isPasswordValid = await user.matchPassword(userData.password);
        if (!isPasswordValid) {
          throw new Error('Invalid password');
        }

        console.log(`   ✅ Login successful: ${userData.username}`);
        testResults.login.passed++;

      } catch (error) {
        console.log(`   ❌ Login failed for ${userData.username}: ${error.message}`);
        testResults.login.failed++;
      }
    }

    // Test 3: Token Generation and Validation
    console.log('\n3️⃣ Testing Token Generation and Validation...');
    for (const userData of testUsers) {
      try {
        const user = await User.findOne({ email: userData.email.toLowerCase() });
        if (!user) {
          throw new Error('User not found');
        }

        // Generate JWT token
        const token = jwt.sign(
          { 
            userId: user._id,
            email: user.email,
            username: user.username
          },
          process.env.JWT_SECRET || 'fallback-secret',
          { expiresIn: '30d' }
        );

        // Validate token
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret');
        
        if (decoded.userId.toString() !== user._id.toString()) {
          throw new Error('Token validation failed - user ID mismatch');
        }

        console.log(`   ✅ Token generated and validated: ${userData.username}`);
        testResults.tokenValidation.passed++;

      } catch (error) {
        console.log(`   ❌ Token test failed for ${userData.username}: ${error.message}`);
        testResults.tokenValidation.failed++;
      }
    }

    // Test 4: Security Features
    console.log('\n4️⃣ Testing Security Features...');
    
    // Test duplicate user prevention
    try {
      const duplicateUser = new User({
        username: testUsers[0].username,
        email: testUsers[0].email,
        password: 'hashedpassword',
        name: 'Duplicate User'
      });
      
      await duplicateUser.save();
      console.log('   ❌ Duplicate user prevention failed');
      testResults.security.failed++;
    } catch (error) {
      if (error.code === 11000) {
        console.log('   ✅ Duplicate user prevention working');
        testResults.security.passed++;
      } else {
        console.log(`   ❌ Unexpected error in duplicate test: ${error.message}`);
        testResults.security.failed++;
      }
    }

    // Test invalid login attempts
    try {
      const user = await User.findOne({ email: testUsers[0].email });
      const isPasswordValid = await user.matchPassword('wrongpassword');

      if (isPasswordValid) {
        console.log('   ❌ Password validation security failed');
        testResults.security.failed++;
      } else {
        console.log('   ✅ Invalid password correctly rejected');
        testResults.security.passed++;
      }
    } catch (error) {
      console.log(`   ❌ Password security test failed: ${error.message}`);
      testResults.security.failed++;
    }

    // Test Results Summary
    console.log('\n📊 Authentication Test Results:');
    console.log('=====================================');
    
    const totalPassed = Object.values(testResults).reduce((sum, test) => sum + test.passed, 0);
    const totalFailed = Object.values(testResults).reduce((sum, test) => sum + test.failed, 0);
    const totalTests = totalPassed + totalFailed;
    
    console.log(`📋 Registration Tests: ${testResults.registration.passed}/${testResults.registration.passed + testResults.registration.failed} passed`);
    console.log(`🔑 Login Tests: ${testResults.login.passed}/${testResults.login.passed + testResults.login.failed} passed`);
    console.log(`🎫 Token Tests: ${testResults.tokenValidation.passed}/${testResults.tokenValidation.passed + testResults.tokenValidation.failed} passed`);
    console.log(`🔒 Security Tests: ${testResults.security.passed}/${testResults.security.passed + testResults.security.failed} passed`);
    
    console.log(`\n🎯 Overall: ${totalPassed}/${totalTests} tests passed (${Math.round((totalPassed/totalTests)*100)}%)`);
    
    if (totalFailed === 0) {
      console.log('\n🎉 ALL AUTHENTICATION TESTS PASSED!');
      console.log('✅ Your authentication system is working correctly.');
    } else {
      console.log(`\n⚠️  ${totalFailed} test(s) failed. Please review and fix the issues.`);
    }

    // Clean up test data
    console.log('\n🧹 Cleaning up test data...');
    await User.deleteMany({ 
      email: { $in: testUsers.map(u => u.email) } 
    });
    console.log('✅ Test data cleaned up');

  } catch (error) {
    console.error('❌ Authentication test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
testAuthenticationFlow()
  .then(() => {
    console.log('\n✅ Authentication flow test completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Test execution failed:', error);
    process.exit(1);
  });
