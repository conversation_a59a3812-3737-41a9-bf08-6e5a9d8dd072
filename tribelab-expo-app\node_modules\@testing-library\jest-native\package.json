{"name": "@testing-library/jest-native", "version": "5.4.3", "description": "Custom jest matchers to test the state of React Native", "main": "dist/index.js", "types": "extend-expect.d.ts", "scripts": {"prepare": "husky install", "commit": "git-cz", "commit:add": "git add .", "commit:all": "npm run commit:add && npm run commit", "readme:toc": "doctoc README.md --maxlevel 3 --title '## Table of Contents'", "test": "jest --colors", "lint": "eslint .", "prepublishOnly": "rm -rf dist && tsc -p tsconfig.prod.json", "semantic-release": "semantic-release", "test:coverage": "jest --coverage --colors", "test:watch": "jest --watch --coverage", "typecheck": "tsc -noEmit"}, "files": ["dist", "extend-expect.js", "extend-expect.d.ts", "legacy-extend-expect.js", "legacy-extend-expect.d.ts"], "keywords": ["testing", "testing-library", "jest", "react-native"], "dependencies": {"chalk": "^4.1.2", "jest-diff": "^29.0.1", "jest-matcher-utils": "^29.0.1", "pretty-format": "^29.0.3", "redent": "^3.0.0"}, "devDependencies": {"@babel/cli": "^7.18.10", "@babel/core": "^7.18.10", "@babel/runtime": "^7.18.9", "@callstack/eslint-config": "^13.0.1", "@relmify/jest-serializer-strip-ansi": "^1.0.2", "@testing-library/react-native": "^12.0.0-rc.0", "@types/jest": "^29.0.1", "@types/react": "^18.0.19", "@types/react-native": "^0.71.0", "@types/react-test-renderer": "^18.0.0", "commitizen": "^4.2.5", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.21.0", "husky": "^8.0.1", "jest": "^29.0.1", "metro-react-native-babel-preset": "0.73.7", "prettier": "^2.7.1", "pretty-quick": "^3.1.3", "react": "18.2.0", "react-native": "0.71.8", "react-test-renderer": "18.2.0", "semantic-release": "^19.0.3", "typescript": "^5.0.4"}, "peerDependencies": {"react": ">=16.0.0", "react-native": ">=0.59", "react-test-renderer": ">=16.0.0"}, "prettier": {"printWidth": 100, "singleQuote": true, "trailingComma": "all", "tabWidth": 2, "proseWrap": "always"}, "eslintConfig": {"extends": "@callstack", "env": {"jest": true}, "rules": {"react-native/no-color-literals": "off", "react-native/no-inline-styles": "off", "react-native/no-raw-text": "off", "react-native-a11y/has-accessibility-hint": "off", "react-native-a11y/has-valid-accessibility-descriptors": "off", "react-native-a11y/has-valid-accessibility-ignores-invert-colors": "off", "react-native-a11y/has-valid-accessibility-value": "off", "@typescript-eslint/consistent-type-imports": "error", "@typescript-eslint/no-explicit-any": "error"}}, "eslintIgnore": ["node_modules/", "dist/", "lib/"], "release": {"branches": ["main"], "plugins": [["@semantic-release/commit-analyzer", {"releaseRules": [{"type": "feature", "release": "minor"}, {"type": "refactor", "release": "patch"}]}], "@semantic-release/release-notes-generator", "@semantic-release/npm", "@semantic-release/github"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "repository": {"type": "git", "url": "git+https://github.com/testing-library/jest-native.git"}, "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/testing-library/jest-native/issues"}, "homepage": "https://github.com/testing-library/jest-native#readme"}