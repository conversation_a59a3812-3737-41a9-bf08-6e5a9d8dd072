var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.default=ensureNativeModuleAvailable;var _NativeRNVectorIcons=_interopRequireDefault(require("./NativeRNVectorIcons"));function ensureNativeModuleAvailable(){if(!_NativeRNVectorIcons.default){throw new Error('The native RNVectorIcons API is not available, did you properly integrate the module? Please verify your autolinking setup and recompile.');}}